"""
Simulation Engine Tests
Comprehensive test suite for the simulation engine
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock

from src.core.simulation_engine import SimulationEngine
from src.core.process_model import ProcessModel, ProcessType
from src.utils.exceptions import SimulationError


class TestSimulationEngine:
    """Test cases for SimulationEngine class"""
    
    @pytest.fixture
    def process_model(self):
        """Create a test process model"""
        parameters = {
            'cycle_time': 60.0,
            'capacity': 1000,
            'efficiency': 0.85
        }
        return ProcessModel(ProcessType.ASSEMBLY.value, parameters)
    
    @pytest.fixture
    def simulation_engine(self, process_model):
        """Create a test simulation engine"""
        return SimulationEngine(
            simulation_id="test-simulation-001",
            process_model=process_model,
            step_size=1.0,
            real_time=False
        )
    
    @pytest.mark.asyncio
    async def test_initialization(self, simulation_engine):
        """Test simulation engine initialization"""
        assert simulation_engine.simulation_id == "test-simulation-001"
        assert simulation_engine.step_size == 1.0
        assert simulation_engine.real_time is False
        assert simulation_engine.current_time == 0.0
        assert simulation_engine.step_number == 0
        assert not simulation_engine.is_initialized
        assert not simulation_engine.is_running
    
    @pytest.mark.asyncio
    async def test_initialize_engine(self, simulation_engine):
        """Test engine initialization process"""
        await simulation_engine.initialize()
        
        assert simulation_engine.is_initialized
        assert simulation_engine.is_running
        assert simulation_engine.current_time == 0.0
        assert simulation_engine.step_number == 0
        assert len(simulation_engine.metrics_history) == 0
        assert len(simulation_engine.events_log) == 0
    
    @pytest.mark.asyncio
    async def test_single_step_execution(self, simulation_engine):
        """Test single step execution"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        
        assert step_result.step_number == 1
        assert step_result.timestamp == 1.0
        assert isinstance(step_result.metrics, dict)
        assert isinstance(step_result.events, list)
        assert simulation_engine.current_time == 1.0
        assert simulation_engine.step_number == 1
    
    @pytest.mark.asyncio
    async def test_multiple_steps(self, simulation_engine):
        """Test multiple step execution"""
        await simulation_engine.initialize()
        
        num_steps = 5
        for i in range(num_steps):
            step_result = await simulation_engine.step()
            assert step_result.step_number == i + 1
            assert step_result.timestamp == (i + 1) * 1.0
        
        assert simulation_engine.current_time == num_steps
        assert simulation_engine.step_number == num_steps
        assert len(simulation_engine.metrics_history) == num_steps
    
    @pytest.mark.asyncio
    async def test_metrics_calculation(self, simulation_engine):
        """Test metrics calculation"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        metrics = step_result.metrics
        
        # Check required metrics
        assert 'throughput' in metrics
        assert 'efficiency' in metrics
        assert 'oee' in metrics
        assert 'quality_rate' in metrics
        assert 'cost_per_unit' in metrics
        assert 'machine_utilization' in metrics
        
        # Check metric ranges
        assert 0 <= metrics['efficiency'] <= 100
        assert 0 <= metrics['quality_rate'] <= 100
        assert 0 <= metrics['oee'] <= 100
        assert 0 <= metrics['machine_utilization'] <= 100
        assert metrics['throughput'] >= 0
        assert metrics['cost_per_unit'] >= 0
    
    @pytest.mark.asyncio
    async def test_event_generation(self, simulation_engine):
        """Test event generation"""
        await simulation_engine.initialize()
        
        # Run multiple steps to potentially generate events
        events_generated = []
        for _ in range(10):
            step_result = await simulation_engine.step()
            events_generated.extend(step_result.events)
        
        # Check event structure if any events were generated
        for event in events_generated:
            assert 'type' in event
            assert 'severity' in event
            assert 'message' in event
            assert 'timestamp' in event
            assert event['severity'] in ['info', 'warning', 'critical']
    
    @pytest.mark.asyncio
    async def test_performance_factor_updates(self, simulation_engine):
        """Test performance factor updates"""
        await simulation_engine.initialize()
        
        initial_efficiency_factor = simulation_engine.efficiency_factor
        initial_quality_factor = simulation_engine.quality_factor
        
        # Run several steps
        for _ in range(20):
            await simulation_engine.step()
        
        # Factors should potentially change (though might be small changes)
        assert isinstance(simulation_engine.efficiency_factor, float)
        assert isinstance(simulation_engine.quality_factor, float)
        assert 0.5 <= simulation_engine.efficiency_factor <= 1.5
        assert 0.5 <= simulation_engine.quality_factor <= 1.5
    
    @pytest.mark.asyncio
    async def test_get_current_metrics(self, simulation_engine):
        """Test getting current metrics"""
        await simulation_engine.initialize()
        
        # Initially no metrics
        metrics = await simulation_engine.get_current_metrics()
        assert metrics == {}
        
        # After a step, should have metrics
        await simulation_engine.step()
        metrics = await simulation_engine.get_current_metrics()
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
    
    @pytest.mark.asyncio
    async def test_get_metrics_history(self, simulation_engine):
        """Test getting metrics history"""
        await simulation_engine.initialize()
        
        # Run several steps
        num_steps = 5
        for _ in range(num_steps):
            await simulation_engine.step()
        
        # Get history
        history = await simulation_engine.get_metrics_history()
        assert len(history) == num_steps
        
        # Check history structure
        for i, entry in enumerate(history):
            assert 'timestamp' in entry
            assert 'step' in entry
            assert entry['step'] == i + 1
            assert entry['timestamp'] == (i + 1) * 1.0
    
    @pytest.mark.asyncio
    async def test_get_events_log(self, simulation_engine):
        """Test getting events log"""
        await simulation_engine.initialize()
        
        # Run several steps
        for _ in range(10):
            await simulation_engine.step()
        
        # Get events log
        events = await simulation_engine.get_events_log()
        assert isinstance(events, list)
        
        # Check event structure if any events exist
        for event in events:
            assert isinstance(event, dict)
            assert 'type' in event
            assert 'timestamp' in event
    
    @pytest.mark.asyncio
    async def test_step_without_initialization(self, simulation_engine):
        """Test step execution without initialization should raise error"""
        with pytest.raises(SimulationError, match="not initialized"):
            await simulation_engine.step()
    
    @pytest.mark.asyncio
    async def test_real_time_simulation(self, process_model):
        """Test real-time simulation timing"""
        engine = SimulationEngine(
            simulation_id="test-realtime",
            process_model=process_model,
            step_size=0.1,  # Small step size for testing
            real_time=True
        )
        
        await engine.initialize()
        
        start_time = time.time()
        await engine.step()
        end_time = time.time()
        
        # Real-time step should take approximately step_size seconds
        # Allow some tolerance for execution overhead
        execution_time = end_time - start_time
        assert execution_time >= 0.05  # At least half the step size
    
    @pytest.mark.asyncio
    async def test_maintenance_scheduling(self, simulation_engine):
        """Test maintenance scheduling functionality"""
        await simulation_engine.initialize()
        
        # Run many steps to potentially trigger maintenance
        for _ in range(100):
            await simulation_engine.step()
        
        # Check if maintenance was scheduled
        assert isinstance(simulation_engine.maintenance_schedule, dict)
    
    @pytest.mark.asyncio
    async def test_efficiency_calculation_edge_cases(self, simulation_engine):
        """Test efficiency calculation with edge cases"""
        await simulation_engine.initialize()
        
        # Mock process result with edge case values
        with patch.object(simulation_engine.process_model, 'execute_step') as mock_step:
            mock_result = Mock()
            mock_result.metrics = {
                'actual_output': 0,
                'theoretical_output': 0,
                'units_produced': 0,
                'defective_units': 0,
                'total_units': 0,
                'cycle_time': 60.0,
                'queue_length': 0,
                'active_machines': 0,
                'total_machines': 1
            }
            mock_result.completed = False
            mock_step.return_value = mock_result
            
            step_result = await simulation_engine.step()
            
            # Should handle zero division gracefully
            assert isinstance(step_result.metrics['efficiency'], float)
            assert step_result.metrics['efficiency'] >= 0
    
    @pytest.mark.asyncio
    async def test_quality_metrics_calculation(self, simulation_engine):
        """Test quality metrics calculation"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        metrics = step_result.metrics
        
        # Quality rate and defect rate should be complementary
        quality_rate = metrics['quality_rate']
        defect_rate = metrics['defect_rate']
        
        assert 0 <= quality_rate <= 100
        assert 0 <= defect_rate <= 100
        # They should roughly add up to 100 (allowing for rounding)
        assert abs((quality_rate + defect_rate) - 100) < 1.0
    
    @pytest.mark.asyncio
    async def test_oee_calculation(self, simulation_engine):
        """Test Overall Equipment Effectiveness calculation"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        oee = step_result.metrics['oee']
        
        assert 0 <= oee <= 100
        
        # OEE should be related to efficiency and quality
        efficiency = step_result.metrics['efficiency']
        quality_rate = step_result.metrics['quality_rate']
        
        # OEE should generally be <= min(efficiency, quality_rate)
        # (allowing for availability factor)
        assert oee <= max(efficiency, quality_rate) + 1  # Small tolerance
    
    @pytest.mark.asyncio
    async def test_cost_calculation(self, simulation_engine):
        """Test cost calculation"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        metrics = step_result.metrics
        
        assert 'material_cost' in metrics
        assert 'labor_cost' in metrics
        assert 'machine_cost' in metrics
        assert 'total_cost' in metrics
        assert 'cost_per_unit' in metrics
        
        # All costs should be non-negative
        assert metrics['material_cost'] >= 0
        assert metrics['labor_cost'] >= 0
        assert metrics['machine_cost'] >= 0
        assert metrics['total_cost'] >= 0
        assert metrics['cost_per_unit'] >= 0
        
        # Total cost should be sum of component costs
        expected_total = (metrics['material_cost'] + 
                         metrics['labor_cost'] + 
                         metrics['machine_cost'])
        assert abs(metrics['total_cost'] - expected_total) < 0.01
    
    @pytest.mark.asyncio
    async def test_resource_utilization(self, simulation_engine):
        """Test resource utilization calculation"""
        await simulation_engine.initialize()
        
        step_result = await simulation_engine.step()
        metrics = step_result.metrics
        
        assert 'machine_utilization' in metrics
        assert 'worker_utilization' in metrics
        assert 'active_machines' in metrics
        assert 'total_machines' in metrics
        
        # Utilization should be percentage
        assert 0 <= metrics['machine_utilization'] <= 100
        assert 0 <= metrics['worker_utilization'] <= 100
        
        # Active should not exceed total
        assert metrics['active_machines'] <= metrics['total_machines']


@pytest.mark.asyncio
async def test_simulation_engine_integration():
    """Integration test for complete simulation workflow"""
    # Create process model
    parameters = {
        'num_machines': 3,
        'cycle_time': 45.0,
        'target_throughput': 80,
        'quality_target': 96.0
    }
    process_model = ProcessModel(ProcessType.MACHINING.value, parameters)
    
    # Create simulation engine
    engine = SimulationEngine(
        simulation_id="integration-test",
        process_model=process_model,
        step_size=2.0,
        real_time=False
    )
    
    # Initialize and run simulation
    await engine.initialize()
    
    # Run for 10 steps
    for i in range(10):
        step_result = await engine.step()
        
        # Verify step progression
        assert step_result.step_number == i + 1
        assert step_result.timestamp == (i + 1) * 2.0
        
        # Verify metrics are reasonable
        metrics = step_result.metrics
        assert 0 <= metrics['efficiency'] <= 100
        assert 0 <= metrics['quality_rate'] <= 100
        assert metrics['throughput'] >= 0
    
    # Verify final state
    assert engine.current_time == 20.0
    assert engine.step_number == 10
    assert len(engine.metrics_history) == 10
    
    # Get final metrics
    final_metrics = await engine.get_current_metrics()
    assert isinstance(final_metrics, dict)
    assert len(final_metrics) > 0
