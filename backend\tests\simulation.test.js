/**
 * Simulation API Tests
 * Comprehensive test suite for simulation endpoints
 */

const request = require('supertest');
const app = require('../src/app');
const { setupTestDB, cleanupTestDB } = require('./helpers/database');
const { createTestUser, getAuthToken } = require('./helpers/auth');

describe('Simulation API', () => {
  let authToken;
  let testUser;

  beforeAll(async () => {
    await setupTestDB();
    testUser = await createTestUser();
    authToken = await getAuthToken(testUser);
  });

  afterAll(async () => {
    await cleanupTestDB();
  });

  describe('POST /api/simulations', () => {
    it('should create a new simulation with valid data', async () => {
      const simulationData = {
        name: 'Test Assembly Line',
        processType: 'assembly',
        parameters: {
          cycleTime: 30,
          capacity: 1000,
          efficiency: 0.85
        },
        duration: 3600,
        stepSize: 1.0,
        autoOptimize: false,
        realTime: false
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe(simulationData.name);
      expect(response.body.data.processType).toBe(simulationData.processType);
      expect(response.body.data.status).toBe('created');
    });

    it('should return 400 for invalid process type', async () => {
      const simulationData = {
        name: 'Invalid Process',
        processType: 'invalid_process',
        parameters: {}
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should return 400 for missing required fields', async () => {
      const simulationData = {
        processType: 'assembly'
        // Missing name and parameters
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should return 401 without authentication', async () => {
      const simulationData = {
        name: 'Unauthorized Test',
        processType: 'assembly',
        parameters: {}
      };

      await request(app)
        .post('/api/simulations')
        .send(simulationData)
        .expect(401);
    });
  });

  describe('GET /api/simulations', () => {
    let testSimulation;

    beforeEach(async () => {
      // Create a test simulation
      const simulationData = {
        name: 'Test Simulation for GET',
        processType: 'machining',
        parameters: { cycleTime: 60 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should return list of simulations', async () => {
      const response = await request(app)
        .get('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.simulations).toBeInstanceOf(Array);
      expect(response.body.data.total).toBeGreaterThan(0);
      expect(response.body.data.limit).toBe(20);
      expect(response.body.data.offset).toBe(0);
    });

    it('should filter simulations by status', async () => {
      const response = await request(app)
        .get('/api/simulations?status=created')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.simulations.forEach(simulation => {
        expect(simulation.status).toBe('created');
      });
    });

    it('should filter simulations by process type', async () => {
      const response = await request(app)
        .get('/api/simulations?processType=machining')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.simulations.forEach(simulation => {
        expect(simulation.processType).toBe('machining');
      });
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/simulations?limit=5&offset=0')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.limit).toBe(5);
      expect(response.body.data.offset).toBe(0);
      expect(response.body.data.simulations.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/simulations/:id', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for GET by ID',
        processType: 'packaging',
        parameters: { batchSize: 50 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should return simulation by ID', async () => {
      const response = await request(app)
        .get(`/api/simulations/${testSimulation.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(testSimulation.id);
      expect(response.body.data.name).toBe(testSimulation.name);
    });

    it('should return 404 for non-existent simulation', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .get(`/api/simulations/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should return 400 for invalid UUID', async () => {
      const response = await request(app)
        .get('/api/simulations/invalid-uuid')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('PUT /api/simulations/:id', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for PUT',
        processType: 'welding',
        parameters: { temperature: 1500 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should update simulation with valid data', async () => {
      const updateData = {
        name: 'Updated Simulation Name',
        parameters: { temperature: 1600, pressure: 2.5 }
      };

      const response = await request(app)
        .put(`/api/simulations/${testSimulation.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.parameters.temperature).toBe(1600);
      expect(response.body.data.parameters.pressure).toBe(2.5);
    });

    it('should return 404 for non-existent simulation', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-************';
      const updateData = { name: 'Updated Name' };

      const response = await request(app)
        .put(`/api/simulations/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/simulations/:id', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for DELETE',
        processType: 'painting',
        parameters: { coats: 3 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should delete simulation', async () => {
      await request(app)
        .delete(`/api/simulations/${testSimulation.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify simulation is deleted
      await request(app)
        .get(`/api/simulations/${testSimulation.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should return 404 for non-existent simulation', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .delete(`/api/simulations/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/simulations/:id/start', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for Start',
        processType: 'injection_molding',
        parameters: { pressure: 150, temperature: 200 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should start simulation', async () => {
      const response = await request(app)
        .post(`/api/simulations/${testSimulation.id}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('started');
    });

    it('should return 404 for non-existent simulation', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-************';

      const response = await request(app)
        .post(`/api/simulations/${fakeId}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/simulations/:id/stop', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for Stop',
        processType: 'quality_control',
        parameters: { inspectionTime: 30 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;

      // Start the simulation first
      await request(app)
        .post(`/api/simulations/${testSimulation.id}/start`)
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should stop simulation', async () => {
      const response = await request(app)
        .post(`/api/simulations/${testSimulation.id}/stop`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('stopped');
    });
  });

  describe('GET /api/simulations/:id/metrics', () => {
    let testSimulation;

    beforeEach(async () => {
      const simulationData = {
        name: 'Test Simulation for Metrics',
        processType: 'material_handling',
        parameters: { speed: 2.5 }
      };

      const response = await request(app)
        .post('/api/simulations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(simulationData);

      testSimulation = response.body.data;
    });

    it('should return simulation metrics', async () => {
      const response = await request(app)
        .get(`/api/simulations/${testSimulation.id}/metrics`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.simulationId).toBe(testSimulation.id);
      expect(response.body.data.timeRange).toBe('1h');
      expect(response.body.data.metrics).toBeDefined();
    });

    it('should support different time ranges', async () => {
      const response = await request(app)
        .get(`/api/simulations/${testSimulation.id}/metrics?timeRange=24h`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.timeRange).toBe('24h');
    });

    it('should return 400 for invalid time range', async () => {
      const response = await request(app)
        .get(`/api/simulations/${testSimulation.id}/metrics?timeRange=invalid`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });
});
