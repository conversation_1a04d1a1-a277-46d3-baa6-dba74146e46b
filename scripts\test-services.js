/**
 * Service Testing Script
 * Tests all ManufactureX services to ensure they're working correctly
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Configuration
const SERVICES = {
  backend: 'http://localhost:5000',
  simulationEngine: 'http://localhost:8000',
  frontend: 'http://localhost:3000'
};

const TIMEOUT = 10000; // 10 seconds

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testService(name, url, endpoint = '/health') {
  const startTime = performance.now();
  
  try {
    const response = await axios.get(`${url}${endpoint}`, {
      timeout: TIMEOUT,
      validateStatus: (status) => status < 500 // Accept 4xx as valid responses
    });
    
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    if (response.status === 200) {
      logSuccess(`${name} is responding (${responseTime}ms) - Status: ${response.status}`);
      return { success: true, responseTime, status: response.status, data: response.data };
    } else {
      logWarning(`${name} responded with status ${response.status} (${responseTime}ms)`);
      return { success: false, responseTime, status: response.status, error: `HTTP ${response.status}` };
    }
  } catch (error) {
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    if (error.code === 'ECONNREFUSED') {
      logError(`${name} is not running or not accessible (${responseTime}ms)`);
      return { success: false, responseTime, error: 'Connection refused' };
    } else if (error.code === 'ETIMEDOUT') {
      logError(`${name} request timed out (${responseTime}ms)`);
      return { success: false, responseTime, error: 'Timeout' };
    } else {
      logError(`${name} error: ${error.message} (${responseTime}ms)`);
      return { success: false, responseTime, error: error.message };
    }
  }
}

async function testBackendAPI() {
  logInfo('Testing Backend API endpoints...');
  
  const endpoints = [
    { name: 'Health Check', path: '/health' },
    { name: 'API Info', path: '/api' },
    { name: 'API Documentation', path: '/api-docs' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testService(`Backend ${endpoint.name}`, SERVICES.backend, endpoint.path);
    results.push({ ...result, endpoint: endpoint.name });
  }
  
  return results;
}

async function testSimulationEngine() {
  logInfo('Testing Simulation Engine endpoints...');
  
  const endpoints = [
    { name: 'Health Check', path: '/health' },
    { name: 'API Info', path: '/' },
    { name: 'Metrics', path: '/metrics' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testService(`Simulation Engine ${endpoint.name}`, SERVICES.simulationEngine, endpoint.path);
    results.push({ ...result, endpoint: endpoint.name });
  }
  
  return results;
}

async function testFrontend() {
  logInfo('Testing Frontend...');
  
  const result = await testService('Frontend', SERVICES.frontend, '/');
  return [result];
}

async function testRateLimiting() {
  logInfo('Testing rate limiting...');
  
  try {
    // Make multiple rapid requests to test rate limiting
    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.get(`${SERVICES.backend}/api/health`, { timeout: TIMEOUT })
          .catch(error => ({ error: error.response?.status || error.code }))
      );
    }
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.some(response => 
      response.error === 429 || (response.status && response.status === 429)
    );
    
    if (rateLimited) {
      logSuccess('Rate limiting is working correctly');
      return { success: true, message: 'Rate limiting active' };
    } else {
      logWarning('Rate limiting may not be configured or limits are high');
      return { success: true, message: 'Rate limiting not triggered' };
    }
  } catch (error) {
    logError(`Rate limiting test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testDatabaseConnections() {
  logInfo('Testing database connections...');
  
  try {
    // Test backend database connection through API
    const response = await axios.get(`${SERVICES.backend}/api/health`, { timeout: TIMEOUT });
    
    if (response.data && response.data.database) {
      logSuccess('Database connection is healthy');
      return { success: true, status: response.data.database };
    } else {
      logWarning('Database status not available in health check');
      return { success: false, error: 'Database status unknown' };
    }
  } catch (error) {
    logError(`Database connection test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function generateTestReport(results) {
  log('\n' + '='.repeat(60), colors.bold);
  log('MANUFACTUREX SERVICE TEST REPORT', colors.bold);
  log('='.repeat(60), colors.bold);
  
  const allResults = results.flat();
  const successCount = allResults.filter(r => r.success).length;
  const totalCount = allResults.length;
  const successRate = Math.round((successCount / totalCount) * 100);
  
  log(`\nOverall Status: ${successCount}/${totalCount} services responding (${successRate}%)`);
  
  if (successRate >= 80) {
    logSuccess('✅ System is healthy and ready for use');
  } else if (successRate >= 60) {
    logWarning('⚠️  System has some issues but core functionality may work');
  } else {
    logError('❌ System has significant issues and may not function properly');
  }
  
  log('\nDetailed Results:');
  log('-'.repeat(40));
  
  allResults.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const endpoint = result.endpoint || 'Service';
    const time = result.responseTime ? `(${result.responseTime}ms)` : '';
    const error = result.error ? ` - ${result.error}` : '';
    
    log(`${status} ${endpoint} ${time}${error}`);
  });
  
  log('\nNext Steps:');
  log('-'.repeat(40));
  
  if (successRate < 100) {
    log('1. Check Docker containers are running: docker-compose ps');
    log('2. View service logs: docker-compose logs [service-name]');
    log('3. Restart failed services: docker-compose restart [service-name]');
    log('4. Check environment configuration in .env file');
  } else {
    log('1. Access the frontend at: http://localhost:3000');
    log('2. View API documentation at: http://localhost:5000/api-docs');
    log('3. Monitor system metrics at: http://localhost:3001 (Grafana)');
  }
  
  log('\n' + '='.repeat(60), colors.bold);
}

async function main() {
  log('🚀 Starting ManufactureX Service Tests...', colors.bold);
  log('This will test all services to ensure they are working correctly.\n');
  
  const results = [];
  
  try {
    // Test all services
    results.push(await testBackendAPI());
    results.push(await testSimulationEngine());
    results.push(await testFrontend());
    
    // Test additional functionality
    const rateLimitResult = await testRateLimiting();
    results.push([rateLimitResult]);
    
    const dbResult = await testDatabaseConnections();
    results.push([dbResult]);
    
    // Generate report
    await generateTestReport(results);
    
  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    logError(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testService,
  testBackendAPI,
  testSimulationEngine,
  testFrontend,
  testRateLimiting,
  testDatabaseConnections
};
