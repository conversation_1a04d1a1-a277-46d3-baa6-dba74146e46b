/**
 * Custom Error Classes
 * Centralized error definitions for the application
 */

/**
 * Base Application Error
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.name = this.constructor.name;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation Error
 */
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400);
    this.errors = errors;
    this.name = 'ValidationError';
  }
}

/**
 * Authentication Error
 */
class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
    this.name = 'AuthenticationError';
  }
}

/**
 * Authorization Error
 */
class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403);
    this.name = 'AuthorizationError';
  }
}

/**
 * Not Found Error
 */
class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

/**
 * Conflict Error
 */
class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409);
    this.name = 'ConflictError';
  }
}

/**
 * Rate Limit Error
 */
class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429);
    this.name = 'RateLimitError';
  }
}

/**
 * Database Error
 */
class DatabaseError extends AppError {
  constructor(message = 'Database error', statusCode = 500) {
    super(message, statusCode);
    this.name = 'DatabaseError';
  }
}

/**
 * External Service Error
 */
class ExternalServiceError extends AppError {
  constructor(message = 'External service error', statusCode = 503) {
    super(message, statusCode);
    this.name = 'ExternalServiceError';
  }
}

/**
 * Simulation Error
 */
class SimulationError extends AppError {
  constructor(message = 'Simulation error', statusCode = 500) {
    super(message, statusCode);
    this.name = 'SimulationError';
  }
}

/**
 * File Upload Error
 */
class FileUploadError extends AppError {
  constructor(message = 'File upload error') {
    super(message, 400);
    this.name = 'FileUploadError';
  }
}

/**
 * Configuration Error
 */
class ConfigurationError extends AppError {
  constructor(message = 'Configuration error') {
    super(message, 500);
    this.name = 'ConfigurationError';
  }
}

/**
 * Network Error
 */
class NetworkError extends AppError {
  constructor(message = 'Network error', statusCode = 503) {
    super(message, statusCode);
    this.name = 'NetworkError';
  }
}

/**
 * Timeout Error
 */
class TimeoutError extends AppError {
  constructor(message = 'Request timeout') {
    super(message, 408);
    this.name = 'TimeoutError';
  }
}

/**
 * Business Logic Error
 */
class BusinessLogicError extends AppError {
  constructor(message = 'Business logic error') {
    super(message, 422);
    this.name = 'BusinessLogicError';
  }
}

/**
 * Error factory for creating specific errors
 */
class ErrorFactory {
  static createValidationError(field, message, value) {
    return new ValidationError('Validation failed', [{
      field,
      message,
      value
    }]);
  }
  
  static createNotFoundError(resource, id) {
    return new NotFoundError(`${resource} with ID ${id} not found`);
  }
  
  static createConflictError(resource, field, value) {
    return new ConflictError(`${resource} with ${field} '${value}' already exists`);
  }
  
  static createDatabaseError(operation, details) {
    return new DatabaseError(`Database ${operation} failed: ${details}`);
  }
  
  static createExternalServiceError(service, operation) {
    return new ExternalServiceError(`${service} ${operation} failed`);
  }
  
  static createSimulationError(operation, simulationId) {
    return new SimulationError(`Simulation ${operation} failed for ${simulationId}`);
  }
  
  static createRateLimitError(limit, window) {
    return new RateLimitError(`Rate limit exceeded: ${limit} requests per ${window}`);
  }
  
  static createTimeoutError(operation, timeout) {
    return new TimeoutError(`${operation} timed out after ${timeout}ms`);
  }
}

/**
 * Error codes for consistent error handling
 */
const ErrorCodes = {
  // Authentication & Authorization
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  ACCESS_DENIED: 'ACCESS_DENIED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // Validation
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Resources
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
  
  // Database
  DATABASE_CONNECTION_FAILED: 'DATABASE_CONNECTION_FAILED',
  DATABASE_QUERY_FAILED: 'DATABASE_QUERY_FAILED',
  DATABASE_CONSTRAINT_VIOLATION: 'DATABASE_CONSTRAINT_VIOLATION',
  
  // External Services
  EXTERNAL_SERVICE_UNAVAILABLE: 'EXTERNAL_SERVICE_UNAVAILABLE',
  EXTERNAL_SERVICE_TIMEOUT: 'EXTERNAL_SERVICE_TIMEOUT',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // Simulation
  SIMULATION_NOT_FOUND: 'SIMULATION_NOT_FOUND',
  SIMULATION_ALREADY_RUNNING: 'SIMULATION_ALREADY_RUNNING',
  SIMULATION_NOT_RUNNING: 'SIMULATION_NOT_RUNNING',
  SIMULATION_INITIALIZATION_FAILED: 'SIMULATION_INITIALIZATION_FAILED',
  SIMULATION_EXECUTION_FAILED: 'SIMULATION_EXECUTION_FAILED',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // File Operations
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
  
  // Network
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  CONNECTION_REFUSED: 'CONNECTION_REFUSED',
  
  // Configuration
  INVALID_CONFIGURATION: 'INVALID_CONFIGURATION',
  MISSING_CONFIGURATION: 'MISSING_CONFIGURATION',
  
  // Business Logic
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INVALID_STATE_TRANSITION: 'INVALID_STATE_TRANSITION',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED'
};

/**
 * HTTP Status Code mappings
 */
const HttpStatusCodes = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
};

module.exports = {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  SimulationError,
  FileUploadError,
  ConfigurationError,
  NetworkError,
  TimeoutError,
  BusinessLogicError,
  ErrorFactory,
  ErrorCodes,
  HttpStatusCodes
};
