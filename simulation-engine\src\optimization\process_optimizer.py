"""
Process Optimizer
Advanced optimization algorithms for manufacturing processes
"""

import asyncio
import logging
import time
import random
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
from scipy.optimize import minimize, differential_evolution
import pulp

logger = logging.getLogger(__name__)

class OptimizationType(Enum):
    """Types of optimization algorithms"""
    GENETIC_ALGORITHM = "genetic_algorithm"
    SIMULATED_ANNEALING = "simulated_annealing"
    LINEAR_PROGRAMMING = "linear_programming"
    PARTICLE_SWARM = "particle_swarm"
    GRADIENT_DESCENT = "gradient_descent"

class ObjectiveType(Enum):
    """Optimization objectives"""
    MAXIMIZE_THROUGHPUT = "maximize_throughput"
    MINIMIZE_COST = "minimize_cost"
    MAXIMIZE_EFFICIENCY = "maximize_efficiency"
    MINIMIZE_CYCLE_TIME = "minimize_cycle_time"
    MAXIMIZE_QUALITY = "maximize_quality"
    MAXIMIZE_OEE = "maximize_oee"

@dataclass
class OptimizationParameter:
    """Parameter for optimization"""
    name: str
    current_value: float
    min_value: float
    max_value: float
    step_size: float = 0.1
    parameter_type: str = "continuous"  # continuous, discrete, integer

@dataclass
class OptimizationConstraint:
    """Constraint for optimization"""
    name: str
    constraint_type: str  # equality, inequality
    expression: str
    value: float

@dataclass
class OptimizationResult:
    """Result of optimization process"""
    success: bool
    optimal_parameters: Dict[str, float]
    objective_value: float
    improvement_percentage: float
    iterations: int
    execution_time: float
    convergence_history: List[float]
    recommendations: List[str]

class ProcessOptimizer:
    """Advanced process optimization using multiple algorithms"""

    def __init__(self, config):
        self.config = config
        self.optimization_history: Dict[str, List[OptimizationResult]] = {}

        # Algorithm implementations
        self.algorithms = {
            OptimizationType.GENETIC_ALGORITHM: self._genetic_algorithm,
            OptimizationType.SIMULATED_ANNEALING: self._simulated_annealing,
            OptimizationType.LINEAR_PROGRAMMING: self._linear_programming,
            OptimizationType.PARTICLE_SWARM: self._particle_swarm_optimization,
            OptimizationType.GRADIENT_DESCENT: self._gradient_descent
        }

    async def optimize(self, simulation_id: str, optimization_params: Dict[str, Any]) -> OptimizationResult:
        """Run optimization for a simulation"""
        try:
            start_time = time.time()

            # Parse optimization parameters
            algorithm_type = OptimizationType(optimization_params.get('algorithm', 'genetic_algorithm'))
            objective_type = ObjectiveType(optimization_params.get('objective', 'maximize_efficiency'))
            parameters = self._parse_parameters(optimization_params.get('parameters', {}))
            constraints = self._parse_constraints(optimization_params.get('constraints', []))

            # Get current performance baseline
            baseline_performance = await self._get_baseline_performance(simulation_id)

            # Run optimization algorithm
            algorithm_func = self.algorithms[algorithm_type]
            result = await algorithm_func(
                simulation_id=simulation_id,
                objective_type=objective_type,
                parameters=parameters,
                constraints=constraints,
                baseline_performance=baseline_performance
            )

            # Calculate execution time
            result.execution_time = time.time() - start_time

            # Store result
            if simulation_id not in self.optimization_history:
                self.optimization_history[simulation_id] = []
            self.optimization_history[simulation_id].append(result)

            logger.info(f"Optimization completed for {simulation_id}: {result.improvement_percentage:.2f}% improvement")

            return result

        except Exception as e:
            logger.error(f"Optimization failed for simulation {simulation_id}: {e}")
            return OptimizationResult(
                success=False,
                optimal_parameters={},
                objective_value=0.0,
                improvement_percentage=0.0,
                iterations=0,
                execution_time=time.time() - start_time,
                convergence_history=[],
                recommendations=[f"Optimization failed: {str(e)}"]
            )

    def _parse_parameters(self, params_dict: Dict[str, Any]) -> List[OptimizationParameter]:
        """Parse optimization parameters from dictionary"""
        parameters = []

        for name, config in params_dict.items():
            param = OptimizationParameter(
                name=name,
                current_value=config.get('current_value', 0.0),
                min_value=config.get('min_value', 0.0),
                max_value=config.get('max_value', 100.0),
                step_size=config.get('step_size', 0.1),
                parameter_type=config.get('type', 'continuous')
            )
            parameters.append(param)

        return parameters

    def _parse_constraints(self, constraints_list: List[Dict[str, Any]]) -> List[OptimizationConstraint]:
        """Parse optimization constraints from list"""
        constraints = []

        for constraint_dict in constraints_list:
            constraint = OptimizationConstraint(
                name=constraint_dict.get('name', ''),
                constraint_type=constraint_dict.get('type', 'inequality'),
                expression=constraint_dict.get('expression', ''),
                value=constraint_dict.get('value', 0.0)
            )
            constraints.append(constraint)

        return constraints

    async def _get_baseline_performance(self, simulation_id: str) -> Dict[str, float]:
        """Get baseline performance metrics for comparison"""
        # This would typically fetch from the simulation manager
        # For now, return simulated baseline values
        return {
            'throughput': 100.0,
            'efficiency': 85.0,
            'quality_rate': 95.0,
            'cost_per_unit': 50.0,
            'cycle_time': 60.0,
            'oee': 80.0
        }

    async def _genetic_algorithm(self, simulation_id: str, objective_type: ObjectiveType,
                                parameters: List[OptimizationParameter],
                                constraints: List[OptimizationConstraint],
                                baseline_performance: Dict[str, float]) -> OptimizationResult:
        """Genetic Algorithm optimization"""
        try:
            # GA parameters
            population_size = 50
            generations = 100
            mutation_rate = 0.1
            crossover_rate = 0.8

            # Initialize population
            population = self._initialize_population(parameters, population_size)
            convergence_history = []

            best_individual = None
            best_fitness = float('-inf')

            for generation in range(generations):
                # Evaluate fitness for each individual
                fitness_scores = []
                for individual in population:
                    fitness = await self._evaluate_fitness(
                        individual, objective_type, parameters, baseline_performance
                    )
                    fitness_scores.append(fitness)

                    if fitness > best_fitness:
                        best_fitness = fitness
                        best_individual = individual.copy()

                convergence_history.append(best_fitness)

                # Selection, crossover, and mutation
                new_population = []

                # Elitism: keep best individuals
                elite_count = int(population_size * 0.1)
                elite_indices = np.argsort(fitness_scores)[-elite_count:]
                for idx in elite_indices:
                    new_population.append(population[idx].copy())

                # Generate rest of population through crossover and mutation
                while len(new_population) < population_size:
                    # Tournament selection
                    parent1 = self._tournament_selection(population, fitness_scores)
                    parent2 = self._tournament_selection(population, fitness_scores)

                    # Crossover
                    if random.random() < crossover_rate:
                        child1, child2 = self._crossover(parent1, parent2, parameters)
                    else:
                        child1, child2 = parent1.copy(), parent2.copy()

                    # Mutation
                    if random.random() < mutation_rate:
                        child1 = self._mutate(child1, parameters)
                    if random.random() < mutation_rate:
                        child2 = self._mutate(child2, parameters)

                    new_population.extend([child1, child2])

                population = new_population[:population_size]

            # Convert best individual to parameter dictionary
            optimal_parameters = {}
            for i, param in enumerate(parameters):
                optimal_parameters[param.name] = best_individual[i]

            # Calculate improvement
            baseline_value = await self._calculate_objective_value(
                [param.current_value for param in parameters],
                objective_type, baseline_performance
            )
            improvement = ((best_fitness - baseline_value) / abs(baseline_value)) * 100

            return OptimizationResult(
                success=True,
                optimal_parameters=optimal_parameters,
                objective_value=best_fitness,
                improvement_percentage=improvement,
                iterations=generations,
                execution_time=0.0,  # Will be set by caller
                convergence_history=convergence_history,
                recommendations=self._generate_optimization_recommendations(optimal_parameters, parameters)
            )

        except Exception as e:
            logger.error(f"Genetic algorithm failed: {e}")
            raise

    def _initialize_population(self, parameters: List[OptimizationParameter],
                              population_size: int) -> List[List[float]]:
        """Initialize random population for genetic algorithm"""
        population = []

        for _ in range(population_size):
            individual = []
            for param in parameters:
                if param.parameter_type == 'integer':
                    value = random.randint(int(param.min_value), int(param.max_value))
                else:
                    value = random.uniform(param.min_value, param.max_value)
                individual.append(value)
            population.append(individual)

        return population

    async def _evaluate_fitness(self, individual: List[float], objective_type: ObjectiveType,
                               parameters: List[OptimizationParameter],
                               baseline_performance: Dict[str, float]) -> float:
        """Evaluate fitness of an individual"""
        return await self._calculate_objective_value(individual, objective_type, baseline_performance)

    async def _calculate_objective_value(self, parameter_values: List[float],
                                        objective_type: ObjectiveType,
                                        baseline_performance: Dict[str, float]) -> float:
        """Calculate objective function value"""
        # Simulate the effect of parameter changes on performance
        # This is a simplified model - in practice, this would run the actual simulation

        # Apply parameter effects (simplified model)
        performance_multiplier = 1.0
        for value in parameter_values:
            # Normalize value to 0-1 range and apply effect
            normalized_value = (value - 50) / 50  # Assuming parameters are around 50-150 range
            performance_multiplier *= (1.0 + normalized_value * 0.1)  # ±10% effect

        if objective_type == ObjectiveType.MAXIMIZE_THROUGHPUT:
            return baseline_performance['throughput'] * performance_multiplier
        elif objective_type == ObjectiveType.MINIMIZE_COST:
            return -baseline_performance['cost_per_unit'] / performance_multiplier
        elif objective_type == ObjectiveType.MAXIMIZE_EFFICIENCY:
            return baseline_performance['efficiency'] * performance_multiplier
        elif objective_type == ObjectiveType.MINIMIZE_CYCLE_TIME:
            return -baseline_performance['cycle_time'] / performance_multiplier
        elif objective_type == ObjectiveType.MAXIMIZE_QUALITY:
            return baseline_performance['quality_rate'] * performance_multiplier
        elif objective_type == ObjectiveType.MAXIMIZE_OEE:
            return baseline_performance['oee'] * performance_multiplier
        else:
            return baseline_performance['efficiency'] * performance_multiplier

    def _tournament_selection(self, population: List[List[float]],
                             fitness_scores: List[float], tournament_size: int = 3) -> List[float]:
        """Tournament selection for genetic algorithm"""
        tournament_indices = random.sample(range(len(population)), tournament_size)
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_index = tournament_indices[np.argmax(tournament_fitness)]
        return population[winner_index].copy()

    def _crossover(self, parent1: List[float], parent2: List[float],
                  parameters: List[OptimizationParameter]) -> Tuple[List[float], List[float]]:
        """Single-point crossover"""
        crossover_point = random.randint(1, len(parent1) - 1)

        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]

        # Ensure values are within bounds
        child1 = self._enforce_bounds(child1, parameters)
        child2 = self._enforce_bounds(child2, parameters)

        return child1, child2

    def _mutate(self, individual: List[float],
               parameters: List[OptimizationParameter]) -> List[float]:
        """Gaussian mutation"""
        mutated = individual.copy()

        for i, param in enumerate(parameters):
            if random.random() < 0.1:  # 10% chance to mutate each gene
                if param.parameter_type == 'integer':
                    mutation = random.randint(-2, 2)
                    mutated[i] = max(param.min_value, min(param.max_value, mutated[i] + mutation))
                else:
                    mutation = random.gauss(0, (param.max_value - param.min_value) * 0.1)
                    mutated[i] = max(param.min_value, min(param.max_value, mutated[i] + mutation))

        return mutated

    def _enforce_bounds(self, individual: List[float],
                       parameters: List[OptimizationParameter]) -> List[float]:
        """Enforce parameter bounds"""
        bounded = []
        for i, param in enumerate(parameters):
            value = max(param.min_value, min(param.max_value, individual[i]))
            if param.parameter_type == 'integer':
                value = int(round(value))
            bounded.append(value)
        return bounded

    async def _simulated_annealing(self, simulation_id: str, objective_type: ObjectiveType,
                                  parameters: List[OptimizationParameter],
                                  constraints: List[OptimizationConstraint],
                                  baseline_performance: Dict[str, float]) -> OptimizationResult:
        """Simulated Annealing optimization"""
        try:
            # SA parameters
            initial_temp = 1000.0
            final_temp = 0.1
            cooling_rate = 0.95
            max_iterations = 1000

            # Initialize with current values
            current_solution = [param.current_value for param in parameters]
            current_fitness = await self._calculate_objective_value(
                current_solution, objective_type, baseline_performance
            )

            best_solution = current_solution.copy()
            best_fitness = current_fitness

            temperature = initial_temp
            convergence_history = []

            for iteration in range(max_iterations):
                # Generate neighbor solution
                neighbor = self._generate_neighbor(current_solution, parameters)
                neighbor_fitness = await self._calculate_objective_value(
                    neighbor, objective_type, baseline_performance
                )

                # Accept or reject neighbor
                delta = neighbor_fitness - current_fitness

                if delta > 0 or random.random() < math.exp(delta / temperature):
                    current_solution = neighbor
                    current_fitness = neighbor_fitness

                    if current_fitness > best_fitness:
                        best_solution = current_solution.copy()
                        best_fitness = current_fitness

                convergence_history.append(best_fitness)

                # Cool down
                temperature *= cooling_rate

                if temperature < final_temp:
                    break

            # Convert to parameter dictionary
            optimal_parameters = {}
            for i, param in enumerate(parameters):
                optimal_parameters[param.name] = best_solution[i]

            # Calculate improvement
            baseline_value = await self._calculate_objective_value(
                [param.current_value for param in parameters],
                objective_type, baseline_performance
            )
            improvement = ((best_fitness - baseline_value) / abs(baseline_value)) * 100

            return OptimizationResult(
                success=True,
                optimal_parameters=optimal_parameters,
                objective_value=best_fitness,
                improvement_percentage=improvement,
                iterations=iteration + 1,
                execution_time=0.0,
                convergence_history=convergence_history,
                recommendations=self._generate_optimization_recommendations(optimal_parameters, parameters)
            )

        except Exception as e:
            logger.error(f"Simulated annealing failed: {e}")
            raise

    def _generate_neighbor(self, solution: List[float],
                          parameters: List[OptimizationParameter]) -> List[float]:
        """Generate neighbor solution for simulated annealing"""
        neighbor = solution.copy()

        # Randomly modify one parameter
        param_index = random.randint(0, len(parameters) - 1)
        param = parameters[param_index]

        if param.parameter_type == 'integer':
            change = random.randint(-2, 2)
            neighbor[param_index] = max(param.min_value,
                                      min(param.max_value, neighbor[param_index] + change))
        else:
            change = random.gauss(0, (param.max_value - param.min_value) * 0.05)
            neighbor[param_index] = max(param.min_value,
                                      min(param.max_value, neighbor[param_index] + change))

        return neighbor

    async def _linear_programming(self, simulation_id: str, objective_type: ObjectiveType,
                                 parameters: List[OptimizationParameter],
                                 constraints: List[OptimizationConstraint],
                                 baseline_performance: Dict[str, float]) -> OptimizationResult:
        """Linear Programming optimization using PuLP"""
        try:
            # Create LP problem
            if objective_type in [ObjectiveType.MAXIMIZE_THROUGHPUT, ObjectiveType.MAXIMIZE_EFFICIENCY,
                                ObjectiveType.MAXIMIZE_QUALITY, ObjectiveType.MAXIMIZE_OEE]:
                prob = pulp.LpProblem("Manufacturing_Optimization", pulp.LpMaximize)
            else:
                prob = pulp.LpProblem("Manufacturing_Optimization", pulp.LpMinimize)

            # Create decision variables
            variables = {}
            for param in parameters:
                if param.parameter_type == 'integer':
                    variables[param.name] = pulp.LpVariable(
                        param.name, lowBound=param.min_value, upBound=param.max_value, cat='Integer'
                    )
                else:
                    variables[param.name] = pulp.LpVariable(
                        param.name, lowBound=param.min_value, upBound=param.max_value, cat='Continuous'
                    )

            # Simplified linear objective function
            # In practice, this would be derived from process models
            objective_coefficients = self._get_linear_coefficients(objective_type)

            objective = pulp.lpSum([
                objective_coefficients.get(param.name, 1.0) * variables[param.name]
                for param in parameters
            ])
            prob += objective

            # Add constraints
            for constraint in constraints:
                # Simplified constraint handling
                # In practice, constraints would be properly parsed
                if constraint.constraint_type == 'inequality':
                    prob += pulp.lpSum([variables[param.name] for param in parameters]) <= constraint.value

            # Solve
            prob.solve(pulp.PULP_CBC_CMD(msg=0))

            if prob.status == pulp.LpStatusOptimal:
                optimal_parameters = {}
                for param in parameters:
                    optimal_parameters[param.name] = variables[param.name].varValue

                objective_value = pulp.value(prob.objective)

                # Calculate improvement
                baseline_value = sum(objective_coefficients.get(param.name, 1.0) * param.current_value
                                   for param in parameters)
                improvement = ((objective_value - baseline_value) / abs(baseline_value)) * 100

                return OptimizationResult(
                    success=True,
                    optimal_parameters=optimal_parameters,
                    objective_value=objective_value,
                    improvement_percentage=improvement,
                    iterations=1,
                    execution_time=0.0,
                    convergence_history=[objective_value],
                    recommendations=self._generate_optimization_recommendations(optimal_parameters, parameters)
                )
            else:
                raise Exception(f"LP solver failed with status: {pulp.LpStatus[prob.status]}")

        except Exception as e:
            logger.error(f"Linear programming failed: {e}")
            raise

    def _get_linear_coefficients(self, objective_type: ObjectiveType) -> Dict[str, float]:
        """Get linear coefficients for objective function"""
        # Simplified coefficients - in practice, these would be derived from process models
        base_coefficients = {
            'cycle_time': -1.0,  # Reducing cycle time is generally good
            'machine_speed': 1.0,  # Increasing speed is generally good
            'temperature': 0.5,   # Moderate effect
            'pressure': 0.3,      # Moderate effect
            'flow_rate': 0.8      # Good effect
        }

        if objective_type == ObjectiveType.MINIMIZE_COST:
            # Invert coefficients for cost minimization
            return {k: -v for k, v in base_coefficients.items()}
        elif objective_type == ObjectiveType.MINIMIZE_CYCLE_TIME:
            return {'cycle_time': -2.0, 'machine_speed': 1.5}
        else:
            return base_coefficients