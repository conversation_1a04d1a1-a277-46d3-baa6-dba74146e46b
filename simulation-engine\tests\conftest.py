"""
Pytest Configuration and Fixtures
Global test configuration for the simulation engine
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

# Set test environment
os.environ['TESTING'] = 'true'
os.environ['LOG_LEVEL'] = 'ERROR'
os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
os.environ['REDIS_URL'] = 'redis://localhost:6379/1'

# Import after setting environment
from src.core.simulation_engine import SimulationEngine
from src.core.process_model import ProcessModel, ProcessType
from src.analytics.performance_analyzer import PerformanceAnalyzer
from src.optimization.process_optimizer import ProcessOptimizer
from src.data_processing.data_collector import DataCollector
from src.utils.config import Config
from src.utils.logger import setup_logger

# Configure pytest
pytest_plugins = ['pytest_asyncio']

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_config():
    """Test configuration fixture"""
    config = Config()
    config.set('TESTING', True)
    config.set('LOG_LEVEL', 'ERROR')
    config.set('DATABASE_URL', 'sqlite:///:memory:')
    config.set('REDIS_URL', 'redis://localhost:6379/1')
    config.set('MAX_CONCURRENT_SIMULATIONS', 5)
    config.set('DEFAULT_STEP_SIZE', 1.0)
    config.set('SIMULATION_TIMEOUT', 300)
    return config

@pytest.fixture(scope="session")
def test_logger():
    """Test logger fixture"""
    return setup_logger('test', {
        'level': 'ERROR',
        'console_output': False,
        'file_path': '/tmp/test.log'
    })

@pytest.fixture
def temp_directory():
    """Create temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)

@pytest.fixture
def sample_process_parameters():
    """Sample process parameters for testing"""
    return {
        'cycle_time': 60.0,
        'capacity': 1000,
        'efficiency': 0.85,
        'quality_target': 95.0,
        'num_machines': 3,
        'batch_size': 50,
        'setup_time': 300.0,
        'maintenance_interval': 8.0
    }

@pytest.fixture
def assembly_process_model(sample_process_parameters):
    """Assembly process model fixture"""
    return ProcessModel(ProcessType.ASSEMBLY.value, sample_process_parameters)

@pytest.fixture
def machining_process_model(sample_process_parameters):
    """Machining process model fixture"""
    parameters = {
        **sample_process_parameters,
        'spindle_speed': 1800.0,
        'feed_rate': 500.0,
        'cutting_depth': 2.0
    }
    return ProcessModel(ProcessType.MACHINING.value, parameters)

@pytest.fixture
def packaging_process_model(sample_process_parameters):
    """Packaging process model fixture"""
    parameters = {
        **sample_process_parameters,
        'packaging_speed': 120.0,
        'label_accuracy': 99.5,
        'seal_temperature': 180.0
    }
    return ProcessModel(ProcessType.PACKAGING.value, parameters)

@pytest.fixture
async def simulation_engine(assembly_process_model):
    """Simulation engine fixture"""
    engine = SimulationEngine(
        simulation_id="test-simulation-001",
        process_model=assembly_process_model,
        step_size=1.0,
        real_time=False
    )
    yield engine
    # Cleanup
    if engine.is_running:
        await engine.stop()

@pytest.fixture
async def initialized_simulation_engine(simulation_engine):
    """Initialized simulation engine fixture"""
    await simulation_engine.initialize()
    yield simulation_engine

@pytest.fixture
def performance_analyzer():
    """Performance analyzer fixture"""
    return PerformanceAnalyzer()

@pytest.fixture
def process_optimizer():
    """Process optimizer fixture"""
    return ProcessOptimizer()

@pytest.fixture
def mock_data_collector():
    """Mock data collector fixture"""
    collector = Mock(spec=DataCollector)
    collector.start = AsyncMock()
    collector.stop = AsyncMock()
    collector.is_running = Mock(return_value=True)
    collector.add_data_source = AsyncMock()
    collector.get_latest_data = AsyncMock()
    collector.get_data_statistics = AsyncMock(return_value={
        'total_points_collected': 1000,
        'points_per_source': {'sensor_01': 500, 'sensor_02': 500},
        'error_count': 0
    })
    return collector

@pytest.fixture
def mock_redis_client():
    """Mock Redis client fixture"""
    client = Mock()
    client.get = AsyncMock(return_value=None)
    client.set = AsyncMock(return_value=True)
    client.delete = AsyncMock(return_value=1)
    client.exists = AsyncMock(return_value=False)
    client.expire = AsyncMock(return_value=True)
    client.ping = AsyncMock(return_value=True)
    return client

@pytest.fixture
def mock_database():
    """Mock database fixture"""
    db = Mock()
    db.connect = AsyncMock()
    db.disconnect = AsyncMock()
    db.execute = AsyncMock()
    db.fetch_all = AsyncMock(return_value=[])
    db.fetch_one = AsyncMock(return_value=None)
    return db

@pytest.fixture
def sample_metrics_data():
    """Sample metrics data for testing"""
    return {
        'throughput': 95.5,
        'efficiency': 87.2,
        'quality_rate': 96.8,
        'oee': 84.3,
        'cycle_time': 58.5,
        'defect_rate': 3.2,
        'machine_utilization': 89.1,
        'worker_utilization': 85.7,
        'cost_per_unit': 12.45,
        'total_cost': 1245.0,
        'material_cost': 800.0,
        'labor_cost': 300.0,
        'machine_cost': 145.0
    }

@pytest.fixture
def sample_optimization_parameters():
    """Sample optimization parameters for testing"""
    return [
        {
            'name': 'cycle_time',
            'current_value': 60.0,
            'min_value': 30.0,
            'max_value': 120.0,
            'parameter_type': 'continuous'
        },
        {
            'name': 'machine_speed',
            'current_value': 1800.0,
            'min_value': 1000.0,
            'max_value': 2500.0,
            'parameter_type': 'continuous'
        },
        {
            'name': 'batch_size',
            'current_value': 50,
            'min_value': 10,
            'max_value': 100,
            'parameter_type': 'integer'
        }
    ]

@pytest.fixture
def sample_optimization_constraints():
    """Sample optimization constraints for testing"""
    return [
        {
            'name': 'quality_constraint',
            'constraint_type': 'inequality',
            'expression': 'quality_rate >= 95',
            'value': 95.0
        },
        {
            'name': 'cost_constraint',
            'constraint_type': 'inequality',
            'expression': 'cost_per_unit <= 15',
            'value': 15.0
        }
    ]

@pytest.fixture
def mock_external_api():
    """Mock external API responses"""
    with patch('aiohttp.ClientSession') as mock_session:
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={'status': 'success', 'data': {}})
        mock_response.text = AsyncMock(return_value='{"status": "success"}')
        
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
        mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
        
        yield mock_session

@pytest.fixture
def mock_file_system(temp_directory):
    """Mock file system operations"""
    with patch('pathlib.Path.exists') as mock_exists, \
         patch('pathlib.Path.mkdir') as mock_mkdir, \
         patch('pathlib.Path.write_text') as mock_write, \
         patch('pathlib.Path.read_text') as mock_read:
        
        mock_exists.return_value = True
        mock_mkdir.return_value = None
        mock_write.return_value = None
        mock_read.return_value = '{"test": "data"}'
        
        yield {
            'exists': mock_exists,
            'mkdir': mock_mkdir,
            'write_text': mock_write,
            'read_text': mock_read
        }

# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def generate_test_id():
        """Generate unique test ID"""
        import uuid
        return f"test_{uuid.uuid4().hex[:8]}"
    
    @staticmethod
    def create_test_metrics(count=10):
        """Create test metrics data"""
        import random
        metrics = []
        for i in range(count):
            metrics.append({
                'timestamp': i * 60,  # Every minute
                'throughput': random.uniform(80, 120),
                'efficiency': random.uniform(75, 95),
                'quality_rate': random.uniform(90, 99),
                'oee': random.uniform(70, 90)
            })
        return metrics
    
    @staticmethod
    def assert_metrics_valid(metrics):
        """Assert that metrics are valid"""
        required_fields = ['throughput', 'efficiency', 'quality_rate', 'oee']
        for field in required_fields:
            assert field in metrics
            assert isinstance(metrics[field], (int, float))
            assert metrics[field] >= 0
    
    @staticmethod
    def assert_simulation_state_valid(engine):
        """Assert that simulation engine state is valid"""
        assert hasattr(engine, 'simulation_id')
        assert hasattr(engine, 'current_time')
        assert hasattr(engine, 'step_number')
        assert engine.current_time >= 0
        assert engine.step_number >= 0

@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils

# Pytest markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow

# Pytest configuration
def pytest_configure(config):
    """Configure pytest"""
    config.addinivalue_line("markers", "unit: mark test as unit test")
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "performance: mark test as performance test")
    config.addinivalue_line("markers", "slow: mark test as slow running")

def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    # Add unit marker to tests without markers
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)

# Async test helpers
@pytest.fixture
async def async_test_helper():
    """Helper for async tests"""
    class AsyncTestHelper:
        @staticmethod
        async def wait_for_condition(condition_func, timeout=5.0, interval=0.1):
            """Wait for a condition to become true"""
            import asyncio
            start_time = asyncio.get_event_loop().time()
            while asyncio.get_event_loop().time() - start_time < timeout:
                if await condition_func():
                    return True
                await asyncio.sleep(interval)
            return False
        
        @staticmethod
        async def run_with_timeout(coro, timeout=5.0):
            """Run coroutine with timeout"""
            return await asyncio.wait_for(coro, timeout=timeout)
    
    return AsyncTestHelper()
