# ManufactureX - Manufacturing Simulation Engine
# Docker Compose Configuration for Development and Production

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: manufacturex-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: manufacturex
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - manufacturex-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: manufacturex-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - manufacturex-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MQTT Broker (Eclipse Mosquitto)
  mqtt:
    image: eclipse-mosquitto:2.0
    container_name: manufacturex-mqtt
    restart: unless-stopped
    volumes:
      - ./docker/mosquitto/config:/mosquitto/config
      - ./docker/mosquitto/data:/mosquitto/data
      - ./docker/mosquitto/log:/mosquitto/log
    ports:
      - "1883:1883"
      - "9001:9001"
    networks:
      - manufacturex-network

  # InfluxDB (Time Series Database)
  influxdb:
    image: influxdb:2.7-alpine
    container_name: manufacturex-influxdb
    restart: unless-stopped
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUXDB_PASSWORD:-influx123}
      DOCKER_INFLUXDB_INIT_ORG: manufacturex
      DOCKER_INFLUXDB_INIT_BUCKET: metrics
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: ${INFLUXDB_TOKEN:-manufacturex-token}
    volumes:
      - influxdb_data:/var/lib/influxdb2
    ports:
      - "8086:8086"
    networks:
      - manufacturex-network

  # Simulation Engine (Python FastAPI)
  simulation-engine:
    build:
      context: ./simulation-engine
      dockerfile: ../docker/Dockerfile.simulation
    container_name: manufacturex-simulation-engine
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app/src
      - DATABASE_URL=postgresql://postgres:${DB_PASSWORD:-password123}@postgres:5432/manufacturex
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - MQTT_BROKER_URL=mqtt://mqtt:1883
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN:-manufacturex-token}
      - INFLUXDB_ORG=manufacturex
      - INFLUXDB_BUCKET=metrics
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - SIMULATION_HOST=0.0.0.0
      - SIMULATION_PORT=8000
    volumes:
      - ./simulation-engine:/app
      - simulation_logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - manufacturex-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Node.js Express)
  backend:
    build:
      context: ./backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: manufacturex-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=5000
      - HOST=0.0.0.0
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=manufacturex
      - DB_USERNAME=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password123}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
      - SIMULATION_ENGINE_HOST=simulation-engine
      - SIMULATION_ENGINE_PORT=8000
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    ports:
      - "5000:5000"
    networks:
      - manufacturex-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      simulation-engine:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React Vite)
  frontend:
    build:
      context: ./frontend
      dockerfile: ../docker/Dockerfile.frontend
      args:
        - VITE_API_URL=${VITE_API_URL:-http://localhost:5000}
        - VITE_WS_URL=${VITE_WS_URL:-ws://localhost:5000}
    container_name: manufacturex-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - manufacturex-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: manufacturex-nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - manufacturex-network
    depends_on:
      - frontend
      - backend
    profiles:
      - production

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: manufacturex-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - manufacturex-network
    profiles:
      - monitoring

  # Grafana (Visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: manufacturex-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - manufacturex-network
    depends_on:
      - prometheus
      - influxdb
    profiles:
      - monitoring

  # Portainer (Container Management)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: manufacturex-portainer
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    ports:
      - "9000:9000"
    networks:
      - manufacturex-network
    profiles:
      - management

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  influxdb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  portainer_data:
    driver: local
  simulation_logs:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  nginx_logs:
    driver: local

networks:
  manufacturex-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
