# ManufactureX API Documentation

## Overview

The ManufactureX API provides comprehensive endpoints for managing manufacturing simulations, digital twins, analytics, and optimization. This RESTful API is built with Node.js/Express and follows OpenAPI 3.0 specifications.

## Base URL

```
Development: http://localhost:5000/api
Production: https://api.manufacturex.com/api
```

## Authentication

All API endpoints require authentication using JWT Bearer tokens.

### Login

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "<PERSON>"
    }
  }
}
```

### Using the Token

Include the token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Simulations API

### Create Simulation

```http
POST /simulations
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "Assembly Line Simulation",
  "processType": "assembly",
  "parameters": {
    "cycleTime": 30,
    "capacity": 1000,
    "efficiency": 0.85
  },
  "duration": 3600,
  "stepSize": 1.0,
  "autoOptimize": false,
  "realTime": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Assembly Line Simulation",
    "processType": "assembly",
    "status": "created",
    "createdAt": "2025-01-10T10:00:00Z",
    "parameters": { ... },
    "progress": 0.0
  }
}
```

### List Simulations

```http
GET /simulations?status=running&limit=20&offset=0
Authorization: Bearer {token}
```

**Query Parameters:**
- `status` (optional): Filter by status (created, running, completed, etc.)
- `processType` (optional): Filter by process type
- `limit` (optional): Number of results (default: 20, max: 100)
- `offset` (optional): Pagination offset (default: 0)

### Get Simulation

```http
GET /simulations/{id}
Authorization: Bearer {token}
```

### Update Simulation

```http
PUT /simulations/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "Updated Simulation Name",
  "parameters": {
    "cycleTime": 25,
    "efficiency": 0.90
  }
}
```

### Delete Simulation

```http
DELETE /simulations/{id}
Authorization: Bearer {token}
```

### Start Simulation

```http
POST /simulations/{id}/start
Authorization: Bearer {token}
```

### Stop Simulation

```http
POST /simulations/{id}/stop
Authorization: Bearer {token}
```

### Get Simulation Metrics

```http
GET /simulations/{id}/metrics?timeRange=1h
Authorization: Bearer {token}
```

**Query Parameters:**
- `timeRange`: 1h, 6h, 24h, 7d, 30d

**Response:**
```json
{
  "success": true,
  "data": {
    "simulationId": "uuid",
    "timeRange": "1h",
    "metrics": {
      "throughput": 95.5,
      "efficiency": 87.2,
      "quality_rate": 96.8,
      "oee": 84.3
    },
    "history": [
      {
        "timestamp": "2025-01-10T10:00:00Z",
        "throughput": 94.2,
        "efficiency": 86.8
      }
    ]
  }
}
```

## Digital Twins API

### Create Digital Twin

```http
POST /digital-twins
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "CNC Machine Twin",
  "twin_type": "machine",
  "description": "Digital twin of CNC Machine #1",
  "physical_asset_id": "machine-001",
  "parameters": {
    "max_temperature": 80.0,
    "max_vibration": 2.0,
    "rated_speed": 1800.0
  },
  "sensors": [
    {
      "type": "temperature",
      "location": "spindle",
      "unit": "celsius"
    }
  ],
  "update_frequency": 1.0
}
```

### List Digital Twins

```http
GET /digital-twins
Authorization: Bearer {token}
```

### Get Digital Twin

```http
GET /digital-twins/{id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "twin_id": "uuid",
    "name": "CNC Machine Twin",
    "type": "machine",
    "status": "active",
    "current_state": {
      "temperature": 45.2,
      "vibration": 0.8,
      "speed": 1750.0,
      "efficiency": 92.5
    },
    "predictions": {
      "maintenance_needed": {
        "probability": 15.2,
        "estimated_time": 72
      }
    },
    "recent_anomalies": []
  }
}
```

## Analytics API

### Get Dashboard Data

```http
GET /analytics/dashboard
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_simulations": 25,
    "active_simulations": 8,
    "average_performance": {
      "efficiency": 87.5,
      "quality_rate": 95.2,
      "oee": 83.1
    },
    "recent_alerts": [
      {
        "type": "quality_alert",
        "severity": "warning",
        "message": "High defect rate detected",
        "timestamp": "2025-01-10T10:30:00Z"
      }
    ]
  }
}
```

### Get Performance Analytics

```http
GET /analytics/performance?timeRange=24h&metrics=efficiency,quality_rate
Authorization: Bearer {token}
```

## Optimization API

### Run Optimization

```http
POST /optimization/{simulation_id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "algorithm": "genetic_algorithm",
  "objective": "maximize_efficiency",
  "parameters": {
    "cycle_time": {
      "current_value": 60.0,
      "min_value": 30.0,
      "max_value": 120.0,
      "type": "continuous"
    },
    "machine_speed": {
      "current_value": 1800.0,
      "min_value": 1000.0,
      "max_value": 2500.0,
      "type": "continuous"
    }
  },
  "constraints": [
    {
      "name": "quality_constraint",
      "type": "inequality",
      "expression": "quality_rate >= 95",
      "value": 95.0
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "optimization_id": "uuid",
    "simulation_id": "uuid",
    "status": "completed",
    "results": {
      "optimal_parameters": {
        "cycle_time": 45.5,
        "machine_speed": 2100.0
      },
      "objective_value": 92.8,
      "improvement_percentage": 8.5,
      "iterations": 150,
      "execution_time": 45.2
    },
    "recommendations": [
      "Reduce cycle time to 45.5 seconds",
      "Increase machine speed to 2100 RPM"
    ]
  }
}
```

## Data Sources API

### List Data Sources

```http
GET /data/sources
Authorization: Bearer {token}
```

### Add Data Source

```http
POST /data/sources
Content-Type: application/json
Authorization: Bearer {token}

{
  "source_id": "machine_01_temp",
  "source_type": "mqtt_sensor",
  "name": "Machine 01 Temperature",
  "description": "Temperature sensor for CNC Machine 01",
  "connection_config": {
    "mqtt_topic": "sensors/machine01/temperature",
    "data_format": "json"
  },
  "data_types": ["sensor_reading"],
  "collection_frequency": 2.0,
  "enabled": true
}
```

### Get Latest Data

```http
GET /data/sources/{source_id}/latest
Authorization: Bearer {token}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Validation Error",
  "message": "Invalid request data",
  "errors": [
    {
      "field": "processType",
      "message": "Invalid process type"
    }
  ],
  "timestamp": "2025-01-10T10:00:00Z"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

API requests are rate limited to 100 requests per 15-minute window per IP address. Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1641811200
```

## WebSocket Events

Real-time updates are available via WebSocket connection:

```javascript
const socket = io('ws://localhost:5000');

// Listen for simulation updates
socket.on('simulation:update', (data) => {
  console.log('Simulation update:', data);
});

// Listen for metrics updates
socket.on('metrics:update', (metrics) => {
  console.log('New metrics:', metrics);
});

// Listen for alerts
socket.on('alert:new', (alert) => {
  console.log('New alert:', alert);
});
```

## SDKs and Libraries

### JavaScript/Node.js

```bash
npm install @manufacturex/sdk
```

```javascript
import { ManufactureXClient } from '@manufacturex/sdk';

const client = new ManufactureXClient({
  apiUrl: 'http://localhost:5000/api',
  apiKey: 'your-api-key'
});

// Create simulation
const simulation = await client.simulations.create({
  name: 'Test Simulation',
  processType: 'assembly',
  parameters: { cycleTime: 30 }
});
```

### Python

```bash
pip install manufacturex-python
```

```python
from manufacturex import ManufactureXClient

client = ManufactureXClient(
    api_url='http://localhost:5000/api',
    api_key='your-api-key'
)

# Create simulation
simulation = client.simulations.create(
    name='Test Simulation',
    process_type='assembly',
    parameters={'cycle_time': 30}
)
```

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.manufacturex.com
- GitHub Issues: https://github.com/HectorTa1989/ManufactureX/issues
