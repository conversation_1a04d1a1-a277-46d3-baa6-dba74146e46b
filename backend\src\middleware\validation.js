/**
 * Validation Middleware
 * Request validation using express-validator
 */

const { body, param, query, validationResult } = require('express-validator');
const { AppError, ValidationError } = require('../utils/errors');

/**
 * Validate request and handle errors
 */
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const validationErrors = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value,
      location: error.location
    }));
    
    const error = new ValidationError('Validation failed', validationErrors);
    return next(error);
  }
  
  next();
};

/**
 * Common validation rules
 */
const validationRules = {
  // User validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  name: body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  
  // UUID validation
  uuid: param('id')
    .isUUID()
    .withMessage('Invalid ID format'),
  
  // Simulation validation
  simulationName: body('name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Simulation name must be between 1 and 255 characters'),
  
  processType: body('processType')
    .isIn(['assembly', 'machining', 'packaging', 'quality_control', 'material_handling', 'welding', 'painting', 'injection_molding'])
    .withMessage('Invalid process type'),
  
  parameters: body('parameters')
    .isObject()
    .withMessage('Parameters must be an object'),
  
  duration: body('duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer'),
  
  stepSize: body('stepSize')
    .optional()
    .isFloat({ min: 0.1 })
    .withMessage('Step size must be at least 0.1'),
  
  // Digital Twin validation
  twinType: body('twin_type')
    .isIn(['machine', 'process', 'factory', 'product', 'system'])
    .withMessage('Invalid twin type'),
  
  updateFrequency: body('update_frequency')
    .optional()
    .isFloat({ min: 0.1 })
    .withMessage('Update frequency must be at least 0.1 seconds'),
  
  // Query validation
  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt()
    .withMessage('Limit must be between 1 and 100'),
  
  offset: query('offset')
    .optional()
    .isInt({ min: 0 })
    .toInt()
    .withMessage('Offset must be a non-negative integer'),
  
  timeRange: query('timeRange')
    .optional()
    .isIn(['1h', '6h', '24h', '7d', '30d'])
    .withMessage('Invalid time range'),
  
  status: query('status')
    .optional()
    .isIn(['created', 'initializing', 'running', 'paused', 'stopped', 'completed', 'error'])
    .withMessage('Invalid status'),
  
  // File upload validation
  fileSize: (maxSize = 10 * 1024 * 1024) => body('file')
    .custom((value, { req }) => {
      if (req.file && req.file.size > maxSize) {
        throw new Error(`File size must be less than ${maxSize / 1024 / 1024}MB`);
      }
      return true;
    }),
  
  fileType: (allowedTypes = ['image/jpeg', 'image/png', 'application/pdf']) => body('file')
    .custom((value, { req }) => {
      if (req.file && !allowedTypes.includes(req.file.mimetype)) {
        throw new Error(`File type must be one of: ${allowedTypes.join(', ')}`);
      }
      return true;
    }),
  
  // Optimization validation
  algorithm: body('algorithm')
    .isIn(['genetic_algorithm', 'simulated_annealing', 'linear_programming', 'particle_swarm', 'gradient_descent'])
    .withMessage('Invalid optimization algorithm'),
  
  objective: body('objective')
    .isIn(['maximize_throughput', 'minimize_cost', 'maximize_efficiency', 'minimize_cycle_time', 'maximize_quality', 'maximize_oee'])
    .withMessage('Invalid optimization objective'),
  
  // Data source validation
  sourceType: body('source_type')
    .isIn(['mqtt_sensor', 'rest_api', 'database', 'file_system', 'simulation', 'manual_input'])
    .withMessage('Invalid source type'),
  
  dataType: body('data_types')
    .isArray()
    .withMessage('Data types must be an array')
    .custom((value) => {
      const validTypes = ['sensor_reading', 'machine_status', 'production_count', 'quality_metric', 'alarm', 'maintenance_log', 'operator_input'];
      const invalidTypes = value.filter(type => !validTypes.includes(type));
      if (invalidTypes.length > 0) {
        throw new Error(`Invalid data types: ${invalidTypes.join(', ')}`);
      }
      return true;
    }),
  
  collectionFrequency: body('collection_frequency')
    .optional()
    .isFloat({ min: 0.1 })
    .withMessage('Collection frequency must be at least 0.1 seconds')
};

/**
 * Validation rule sets for different endpoints
 */
const validationSets = {
  // User validation sets
  register: [
    validationRules.name,
    validationRules.email,
    validationRules.password,
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Password confirmation does not match password');
        }
        return true;
      })
  ],
  
  login: [
    validationRules.email,
    body('password').notEmpty().withMessage('Password is required')
  ],
  
  // Simulation validation sets
  createSimulation: [
    validationRules.simulationName,
    validationRules.processType,
    validationRules.parameters,
    validationRules.duration,
    validationRules.stepSize,
    body('autoOptimize').optional().isBoolean(),
    body('realTime').optional().isBoolean()
  ],
  
  updateSimulation: [
    validationRules.uuid,
    body('name').optional().trim().isLength({ min: 1, max: 255 }),
    body('parameters').optional().isObject(),
    validationRules.duration,
    validationRules.stepSize,
    body('autoOptimize').optional().isBoolean()
  ],
  
  getSimulation: [validationRules.uuid],
  
  getSimulations: [
    validationRules.limit,
    validationRules.offset,
    validationRules.status,
    validationRules.processType
  ],
  
  getSimulationMetrics: [
    validationRules.uuid,
    validationRules.timeRange
  ],
  
  // Digital Twin validation sets
  createDigitalTwin: [
    body('name').trim().isLength({ min: 1, max: 255 }),
    validationRules.twinType,
    body('description').optional().trim().isLength({ max: 1000 }),
    body('physical_asset_id').optional().trim(),
    body('parameters').optional().isObject(),
    body('sensors').optional().isArray(),
    body('actuators').optional().isArray(),
    validationRules.updateFrequency
  ],
  
  // Optimization validation sets
  runOptimization: [
    validationRules.uuid,
    validationRules.algorithm,
    validationRules.objective,
    body('parameters').isObject(),
    body('constraints').optional().isArray()
  ],
  
  // Data source validation sets
  createDataSource: [
    body('source_id').trim().isLength({ min: 1, max: 100 }),
    validationRules.sourceType,
    body('name').trim().isLength({ min: 1, max: 255 }),
    body('description').optional().trim().isLength({ max: 1000 }),
    body('connection_config').isObject(),
    validationRules.dataType,
    validationRules.collectionFrequency,
    body('enabled').optional().isBoolean()
  ]
};

/**
 * Sanitize input data
 */
const sanitizeInput = (req, res, next) => {
  // Remove any null bytes
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return value.replace(/\0/g, '');
    }
    if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        value[key] = sanitizeValue(value[key]);
      }
    }
    return value;
  };

  req.body = sanitizeValue(req.body);
  req.query = sanitizeValue(req.query);
  req.params = sanitizeValue(req.params);
  
  next();
};

/**
 * Custom validation for complex objects
 */
const validateComplexObject = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));
      
      const validationError = new ValidationError('Validation failed', validationErrors);
      return next(validationError);
    }
    next();
  };
};

module.exports = {
  validateRequest,
  validationRules,
  validationSets,
  sanitizeInput,
  validateComplexObject
};
