"""
Simulation Manager
Core component for managing manufacturing simulations
"""

import asyncio
import uuid
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from .simulation_engine import SimulationEngine
from .process_model import ProcessModel
from ..utils.exceptions import SimulationError, ValidationError

logger = logging.getLogger(__name__)

class SimulationStatus(Enum):
    """Simulation status enumeration"""
    CREATED = "created"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class SimulationConfig:
    """Simulation configuration data class"""
    name: str
    process_type: str
    parameters: Dict[str, Any]
    duration: Optional[int] = None
    step_size: float = 1.0
    auto_optimize: bool = False
    real_time: bool = False
    
    def validate(self):
        """Validate simulation configuration"""
        if not self.name or not isinstance(self.name, str):
            raise ValidationError("Simulation name is required and must be a string")
        
        if not self.process_type or not isinstance(self.process_type, str):
            raise ValidationError("Process type is required and must be a string")
        
        if not isinstance(self.parameters, dict):
            raise ValidationError("Parameters must be a dictionary")
        
        if self.step_size <= 0:
            raise ValidationError("Step size must be positive")

@dataclass
class SimulationState:
    """Current state of a simulation"""
    simulation_id: str
    config: SimulationConfig
    status: SimulationStatus
    current_time: float = 0.0
    progress: float = 0.0
    created_at: float = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
        if self.metrics is None:
            self.metrics = {}

class SimulationManager:
    """Manages multiple manufacturing simulations"""
    
    def __init__(self, config):
        self.config = config
        self.simulations: Dict[str, SimulationState] = {}
        self.engines: Dict[str, SimulationEngine] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self._initialized = False
        
    async def initialize(self):
        """Initialize the simulation manager"""
        try:
            logger.info("Initializing Simulation Manager...")
            # Initialize any required resources
            self._initialized = True
            logger.info("Simulation Manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Simulation Manager: {e}")
            raise SimulationError(f"Initialization failed: {e}")
    
    async def shutdown(self):
        """Shutdown the simulation manager"""
        try:
            logger.info("Shutting down Simulation Manager...")
            
            # Stop all running simulations
            for simulation_id in list(self.running_tasks.keys()):
                await self.stop_simulation(simulation_id)
            
            # Clean up resources
            self.simulations.clear()
            self.engines.clear()
            self.running_tasks.clear()
            
            self._initialized = False
            logger.info("Simulation Manager shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def is_healthy(self) -> bool:
        """Check if the simulation manager is healthy"""
        return self._initialized
    
    async def create_simulation(self, config_dict: Dict[str, Any]) -> str:
        """Create a new simulation"""
        try:
            # Validate and create configuration
            config = SimulationConfig(**config_dict)
            config.validate()
            
            # Generate unique simulation ID
            simulation_id = str(uuid.uuid4())
            
            # Create simulation state
            state = SimulationState(
                simulation_id=simulation_id,
                config=config,
                status=SimulationStatus.CREATED
            )
            
            # Create process model
            process_model = ProcessModel(config.process_type, config.parameters)
            
            # Create simulation engine
            engine = SimulationEngine(
                simulation_id=simulation_id,
                process_model=process_model,
                step_size=config.step_size,
                real_time=config.real_time
            )
            
            # Store simulation
            self.simulations[simulation_id] = state
            self.engines[simulation_id] = engine
            
            logger.info(f"Created simulation {simulation_id}: {config.name}")
            return simulation_id
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to create simulation: {e}")
            raise SimulationError(f"Failed to create simulation: {e}")
    
    async def start_simulation(self, simulation_id: str):
        """Start a simulation"""
        try:
            if simulation_id not in self.simulations:
                raise SimulationError(f"Simulation {simulation_id} not found")
            
            state = self.simulations[simulation_id]
            engine = self.engines[simulation_id]
            
            if state.status == SimulationStatus.RUNNING:
                logger.warning(f"Simulation {simulation_id} is already running")
                return
            
            # Update state
            state.status = SimulationStatus.INITIALIZING
            state.started_at = time.time()
            
            # Start simulation task
            task = asyncio.create_task(self._run_simulation(simulation_id))
            self.running_tasks[simulation_id] = task
            
            logger.info(f"Started simulation {simulation_id}")
            
        except Exception as e:
            logger.error(f"Failed to start simulation {simulation_id}: {e}")
            if simulation_id in self.simulations:
                self.simulations[simulation_id].status = SimulationStatus.ERROR
                self.simulations[simulation_id].error_message = str(e)
            raise SimulationError(f"Failed to start simulation: {e}")
    
    async def stop_simulation(self, simulation_id: str):
        """Stop a simulation"""
        try:
            if simulation_id not in self.simulations:
                raise SimulationError(f"Simulation {simulation_id} not found")
            
            state = self.simulations[simulation_id]
            
            # Cancel running task
            if simulation_id in self.running_tasks:
                task = self.running_tasks[simulation_id]
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                del self.running_tasks[simulation_id]
            
            # Update state
            state.status = SimulationStatus.STOPPED
            state.completed_at = time.time()
            
            logger.info(f"Stopped simulation {simulation_id}")
            
        except Exception as e:
            logger.error(f"Failed to stop simulation {simulation_id}: {e}")
            raise SimulationError(f"Failed to stop simulation: {e}")
    
    async def _run_simulation(self, simulation_id: str):
        """Run simulation loop"""
        try:
            state = self.simulations[simulation_id]
            engine = self.engines[simulation_id]
            config = state.config
            
            # Initialize engine
            await engine.initialize()
            state.status = SimulationStatus.RUNNING
            
            # Run simulation loop
            start_time = time.time()
            while state.status == SimulationStatus.RUNNING:
                # Execute simulation step
                step_result = await engine.step()
                
                # Update state
                state.current_time = engine.current_time
                state.metrics.update(step_result.metrics)
                
                # Calculate progress
                if config.duration:
                    state.progress = min(state.current_time / config.duration, 1.0)
                    if state.progress >= 1.0:
                        break
                else:
                    # For indefinite simulations, calculate based on time elapsed
                    elapsed = time.time() - start_time
                    state.progress = min(elapsed / 3600, 1.0)  # Progress over 1 hour
                
                # Check for completion conditions
                if step_result.completed:
                    break
                
                # Sleep for real-time simulation
                if config.real_time:
                    await asyncio.sleep(config.step_size)
                else:
                    # Yield control for non-real-time simulations
                    await asyncio.sleep(0)
            
            # Complete simulation
            state.status = SimulationStatus.COMPLETED
            state.completed_at = time.time()
            state.progress = 1.0
            
            logger.info(f"Simulation {simulation_id} completed successfully")
            
        except asyncio.CancelledError:
            logger.info(f"Simulation {simulation_id} was cancelled")
            raise
        except Exception as e:
            logger.error(f"Simulation {simulation_id} failed: {e}")
            state.status = SimulationStatus.ERROR
            state.error_message = str(e)
            state.completed_at = time.time()
        finally:
            # Clean up
            if simulation_id in self.running_tasks:
                del self.running_tasks[simulation_id]
    
    async def get_simulation(self, simulation_id: str) -> Optional[Dict[str, Any]]:
        """Get simulation details"""
        if simulation_id not in self.simulations:
            return None
        
        state = self.simulations[simulation_id]
        return {
            "simulation_id": simulation_id,
            "name": state.config.name,
            "process_type": state.config.process_type,
            "status": state.status.value,
            "current_time": state.current_time,
            "progress": state.progress,
            "created_at": state.created_at,
            "started_at": state.started_at,
            "completed_at": state.completed_at,
            "error_message": state.error_message,
            "metrics": state.metrics,
            "config": asdict(state.config)
        }
    
    async def list_simulations(self) -> List[Dict[str, Any]]:
        """List all simulations"""
        simulations = []
        for simulation_id in self.simulations:
            sim_data = await self.get_simulation(simulation_id)
            if sim_data:
                simulations.append(sim_data)
        return simulations
    
    async def get_simulation_metrics(self, simulation_id: str) -> Dict[str, Any]:
        """Get current simulation metrics"""
        if simulation_id not in self.simulations:
            raise SimulationError(f"Simulation {simulation_id} not found")
        
        state = self.simulations[simulation_id]
        engine = self.engines.get(simulation_id)
        
        metrics = state.metrics.copy()
        
        if engine:
            # Get real-time metrics from engine
            engine_metrics = await engine.get_current_metrics()
            metrics.update(engine_metrics)
        
        return metrics
