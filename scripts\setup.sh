#!/bin/bash

# ManufactureX Setup Script
# Automated setup for development and production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ManufactureX"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
ENV_EXAMPLE=".env.example"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    local missing_deps=()
    
    # Check Docker
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    # Check Docker Compose
    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        missing_deps+=("docker-compose")
    fi
    
    # Check Node.js
    if ! command_exists node; then
        missing_deps+=("node.js")
    fi
    
    # Check Python
    if ! command_exists python3; then
        missing_deps+=("python3")
    fi
    
    # Check Git
    if ! command_exists git; then
        missing_deps+=("git")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        print_status "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    print_success "All required dependencies are installed"
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f "$ENV_EXAMPLE" ]; then
            cp "$ENV_EXAMPLE" "$ENV_FILE"
            print_success "Created $ENV_FILE from $ENV_EXAMPLE"
            print_warning "Please review and update the environment variables in $ENV_FILE"
        else
            print_error "$ENV_EXAMPLE not found"
            exit 1
        fi
    else
        print_warning "$ENV_FILE already exists, skipping creation"
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    local directories=(
        "logs"
        "uploads"
        "data"
        "temp"
        "models"
        "backups"
        "docker/mosquitto/config"
        "docker/mosquitto/data"
        "docker/mosquitto/log"
        "docker/nginx/conf.d"
        "docker/prometheus"
        "docker/grafana/provisioning"
        "docker/grafana/dashboards"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
    
    print_success "All directories created"
}

# Function to setup Docker networks
setup_docker_networks() {
    print_status "Setting up Docker networks..."
    
    # Check if network already exists
    if ! docker network ls | grep -q "manufacturex-network"; then
        docker network create manufacturex-network --driver bridge --subnet=172.20.0.0/16
        print_success "Created Docker network: manufacturex-network"
    else
        print_warning "Docker network manufacturex-network already exists"
    fi
}

# Function to install backend dependencies
install_backend_deps() {
    print_status "Installing backend dependencies..."
    
    if [ -d "backend" ]; then
        cd backend
        if [ -f "package.json" ]; then
            npm install
            print_success "Backend dependencies installed"
        else
            print_error "package.json not found in backend directory"
            exit 1
        fi
        cd ..
    else
        print_error "Backend directory not found"
        exit 1
    fi
}

# Function to install frontend dependencies
install_frontend_deps() {
    print_status "Installing frontend dependencies..."
    
    if [ -d "frontend" ]; then
        cd frontend
        if [ -f "package.json" ]; then
            npm install
            print_success "Frontend dependencies installed"
        else
            print_error "package.json not found in frontend directory"
            exit 1
        fi
        cd ..
    else
        print_error "Frontend directory not found"
        exit 1
    fi
}

# Function to install Python dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."
    
    if [ -d "simulation-engine" ]; then
        cd simulation-engine
        if [ -f "requirements.txt" ]; then
            # Create virtual environment if it doesn't exist
            if [ ! -d "venv" ]; then
                python3 -m venv venv
                print_status "Created Python virtual environment"
            fi
            
            # Activate virtual environment and install dependencies
            source venv/bin/activate
            pip install --upgrade pip
            pip install -r requirements.txt
            print_success "Python dependencies installed"
            deactivate
        else
            print_error "requirements.txt not found in simulation-engine directory"
            exit 1
        fi
        cd ..
    else
        print_error "simulation-engine directory not found"
        exit 1
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Start PostgreSQL container
    docker-compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    print_status "Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Run database migrations
    if [ -d "backend" ]; then
        cd backend
        npm run db:migrate
        print_success "Database migrations completed"
        
        # Seed database if in development
        if grep -q "NODE_ENV=development" ../$ENV_FILE; then
            npm run db:seed
            print_success "Database seeded with development data"
        fi
        cd ..
    fi
}

# Function to build Docker images
build_docker_images() {
    print_status "Building Docker images..."
    
    docker-compose build
    print_success "Docker images built successfully"
}

# Function to start services
start_services() {
    print_status "Starting services..."
    
    # Start core services
    docker-compose up -d postgres redis mqtt influxdb
    
    # Wait for services to be ready
    print_status "Waiting for core services to be ready..."
    sleep 15
    
    # Start application services
    docker-compose up -d simulation-engine backend frontend
    
    print_success "All services started"
}

# Function to verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    local services=(
        "postgres:5432"
        "redis:6379"
        "simulation-engine:8000"
        "backend:5000"
        "frontend:3000"
    )
    
    for service in "${services[@]}"; do
        local host=$(echo $service | cut -d: -f1)
        local port=$(echo $service | cut -d: -f2)
        
        if docker-compose ps | grep -q "$host.*Up"; then
            print_success "$host service is running"
        else
            print_error "$host service is not running"
        fi
    done
    
    # Test API endpoints
    print_status "Testing API endpoints..."
    
    # Wait a bit for services to fully start
    sleep 10
    
    # Test backend health
    if curl -f http://localhost:5000/health >/dev/null 2>&1; then
        print_success "Backend API is responding"
    else
        print_warning "Backend API is not responding yet"
    fi
    
    # Test simulation engine health
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        print_success "Simulation Engine is responding"
    else
        print_warning "Simulation Engine is not responding yet"
    fi
    
    # Test frontend
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        print_success "Frontend is responding"
    else
        print_warning "Frontend is not responding yet"
    fi
}

# Function to display final information
display_final_info() {
    print_success "🎉 $PROJECT_NAME setup completed successfully!"
    echo
    echo "📋 Service URLs:"
    echo "   Frontend:          http://localhost:3000"
    echo "   Backend API:       http://localhost:5000"
    echo "   Simulation Engine: http://localhost:8000"
    echo "   API Documentation: http://localhost:5000/api-docs"
    echo
    echo "🔧 Management URLs:"
    echo "   InfluxDB:          http://localhost:8086"
    echo "   Grafana:           http://localhost:3001 (admin/admin123)"
    echo "   Portainer:         http://localhost:9000"
    echo
    echo "📚 Useful commands:"
    echo "   View logs:         docker-compose logs -f [service]"
    echo "   Stop services:     docker-compose down"
    echo "   Restart services:  docker-compose restart"
    echo "   Update services:   docker-compose pull && docker-compose up -d"
    echo
    echo "⚠️  Important notes:"
    echo "   - Review and update environment variables in $ENV_FILE"
    echo "   - Change default passwords in production"
    echo "   - Services may take a few minutes to fully start"
    echo
}

# Main setup function
main() {
    echo "🚀 Setting up $PROJECT_NAME..."
    echo
    
    # Parse command line arguments
    local skip_deps=false
    local skip_docker=false
    local production=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --skip-docker)
                skip_docker=true
                shift
                ;;
            --production)
                production=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-deps     Skip dependency installation"
                echo "  --skip-docker   Skip Docker setup"
                echo "  --production    Setup for production environment"
                echo "  -h, --help      Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run setup steps
    check_requirements
    setup_environment
    create_directories
    
    if [ "$skip_docker" = false ]; then
        setup_docker_networks
    fi
    
    if [ "$skip_deps" = false ]; then
        install_backend_deps
        install_frontend_deps
        install_python_deps
    fi
    
    if [ "$skip_docker" = false ]; then
        build_docker_images
        start_services
        setup_database
        verify_installation
    fi
    
    display_final_info
}

# Run main function with all arguments
main "$@"
