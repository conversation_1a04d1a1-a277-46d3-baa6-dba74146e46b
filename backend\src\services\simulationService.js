/**
 * Simulation Service
 * Business logic for simulation management
 */

const axios = require('axios');
const config = require('../config/config');
const logger = require('../utils/logger');
const { 
  AppError, 
  NotFoundError, 
  SimulationError, 
  ExternalServiceError 
} = require('../utils/errors');

class SimulationService {
  constructor() {
    this.simulationEngineUrl = `http://${config.simulationEngine.host}:${config.simulationEngine.port}`;
    this.simulations = new Map(); // In-memory storage for demo
  }

  /**
   * Get all simulations with filtering and pagination
   */
  async getAllSimulations(filters = {}, limit = 20, offset = 0) {
    try {
      // In a real application, this would query the database
      const allSimulations = Array.from(this.simulations.values());
      
      // Apply filters
      let filteredSimulations = allSimulations;
      
      if (filters.status) {
        filteredSimulations = filteredSimulations.filter(sim => sim.status === filters.status);
      }
      
      if (filters.processType) {
        filteredSimulations = filteredSimulations.filter(sim => sim.processType === filters.processType);
      }
      
      // Apply pagination
      const total = filteredSimulations.length;
      const simulations = filteredSimulations.slice(offset, offset + limit);
      
      return {
        simulations,
        total,
        limit,
        offset
      };
    } catch (error) {
      logger.error('Failed to get simulations:', error);
      throw new AppError('Failed to retrieve simulations', 500);
    }
  }

  /**
   * Create a new simulation
   */
  async createSimulation(simulationData) {
    try {
      const simulationId = this.generateId();
      
      const simulation = {
        id: simulationId,
        name: simulationData.name,
        processType: simulationData.processType,
        parameters: simulationData.parameters,
        duration: simulationData.duration || 3600,
        stepSize: simulationData.stepSize || 1.0,
        autoOptimize: simulationData.autoOptimize || false,
        realTime: simulationData.realTime || false,
        userId: simulationData.userId,
        status: 'created',
        progress: 0.0,
        createdAt: new Date().toISOString(),
        startedAt: null,
        completedAt: null,
        metrics: {}
      };

      // Call simulation engine to create simulation
      const response = await this.callSimulationEngine('POST', '/simulations', {
        simulation_id: simulationId,
        process_type: simulationData.processType,
        parameters: simulationData.parameters,
        step_size: simulationData.stepSize,
        real_time: simulationData.realTime
      });

      if (response.success) {
        this.simulations.set(simulationId, simulation);
        logger.info(`Simulation created: ${simulationId}`);
        return simulation;
      } else {
        throw new SimulationError('Failed to create simulation in engine');
      }
    } catch (error) {
      logger.error('Failed to create simulation:', error);
      if (error instanceof SimulationError) {
        throw error;
      }
      throw new AppError('Failed to create simulation', 500);
    }
  }

  /**
   * Get simulation by ID
   */
  async getSimulationById(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      // Get latest status from simulation engine
      try {
        const response = await this.callSimulationEngine('GET', `/simulations/${simulationId}`);
        if (response.success) {
          simulation.status = response.data.status;
          simulation.progress = response.data.progress;
          simulation.metrics = response.data.metrics;
        }
      } catch (engineError) {
        logger.warning('Failed to get simulation status from engine:', engineError);
      }

      return simulation;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      logger.error(`Failed to get simulation ${simulationId}:`, error);
      throw new AppError('Failed to retrieve simulation', 500);
    }
  }

  /**
   * Update simulation
   */
  async updateSimulation(simulationId, updateData) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      // Check if simulation can be updated
      if (simulation.status === 'running') {
        throw new SimulationError('Cannot update running simulation');
      }

      // Update simulation data
      const updatedSimulation = {
        ...simulation,
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      this.simulations.set(simulationId, updatedSimulation);
      logger.info(`Simulation updated: ${simulationId}`);
      
      return updatedSimulation;
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to update simulation ${simulationId}:`, error);
      throw new AppError('Failed to update simulation', 500);
    }
  }

  /**
   * Delete simulation
   */
  async deleteSimulation(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      // Stop simulation if running
      if (simulation.status === 'running') {
        await this.stopSimulation(simulationId);
      }

      // Delete from simulation engine
      try {
        await this.callSimulationEngine('DELETE', `/simulations/${simulationId}`);
      } catch (engineError) {
        logger.warning('Failed to delete simulation from engine:', engineError);
      }

      // Delete from local storage
      this.simulations.delete(simulationId);
      logger.info(`Simulation deleted: ${simulationId}`);
      
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      logger.error(`Failed to delete simulation ${simulationId}:`, error);
      throw new AppError('Failed to delete simulation', 500);
    }
  }

  /**
   * Start simulation
   */
  async startSimulation(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      if (simulation.status === 'running') {
        throw new SimulationError('Simulation is already running');
      }

      // Start simulation in engine
      const response = await this.callSimulationEngine('POST', `/simulations/${simulationId}/start`);
      
      if (response.success) {
        simulation.status = 'running';
        simulation.startedAt = new Date().toISOString();
        this.simulations.set(simulationId, simulation);
        
        logger.info(`Simulation started: ${simulationId}`);
        return { success: true, simulation };
      } else {
        throw new SimulationError('Failed to start simulation in engine');
      }
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to start simulation ${simulationId}:`, error);
      throw new AppError('Failed to start simulation', 500);
    }
  }

  /**
   * Stop simulation
   */
  async stopSimulation(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      if (simulation.status !== 'running') {
        throw new SimulationError('Simulation is not running');
      }

      // Stop simulation in engine
      const response = await this.callSimulationEngine('POST', `/simulations/${simulationId}/stop`);
      
      if (response.success) {
        simulation.status = 'stopped';
        simulation.completedAt = new Date().toISOString();
        this.simulations.set(simulationId, simulation);
        
        logger.info(`Simulation stopped: ${simulationId}`);
        return { success: true, simulation };
      } else {
        throw new SimulationError('Failed to stop simulation in engine');
      }
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to stop simulation ${simulationId}:`, error);
      throw new AppError('Failed to stop simulation', 500);
    }
  }

  /**
   * Pause simulation
   */
  async pauseSimulation(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      if (simulation.status !== 'running') {
        throw new SimulationError('Simulation is not running');
      }

      // Pause simulation in engine
      const response = await this.callSimulationEngine('POST', `/simulations/${simulationId}/pause`);
      
      if (response.success) {
        simulation.status = 'paused';
        this.simulations.set(simulationId, simulation);
        
        logger.info(`Simulation paused: ${simulationId}`);
        return { success: true, simulation };
      } else {
        throw new SimulationError('Failed to pause simulation in engine');
      }
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to pause simulation ${simulationId}:`, error);
      throw new AppError('Failed to pause simulation', 500);
    }
  }

  /**
   * Resume simulation
   */
  async resumeSimulation(simulationId) {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      if (simulation.status !== 'paused') {
        throw new SimulationError('Simulation is not paused');
      }

      // Resume simulation in engine
      const response = await this.callSimulationEngine('POST', `/simulations/${simulationId}/resume`);
      
      if (response.success) {
        simulation.status = 'running';
        this.simulations.set(simulationId, simulation);
        
        logger.info(`Simulation resumed: ${simulationId}`);
        return { success: true, simulation };
      } else {
        throw new SimulationError('Failed to resume simulation in engine');
      }
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to resume simulation ${simulationId}:`, error);
      throw new AppError('Failed to resume simulation', 500);
    }
  }

  /**
   * Get simulation metrics
   */
  async getSimulationMetrics(simulationId, timeRange = '1h') {
    try {
      const simulation = this.simulations.get(simulationId);
      
      if (!simulation) {
        throw new NotFoundError(`Simulation with ID ${simulationId} not found`);
      }

      // Get metrics from simulation engine
      const response = await this.callSimulationEngine('GET', `/simulations/${simulationId}/metrics?time_range=${timeRange}`);
      
      if (response.success) {
        return {
          current: response.data.current_metrics,
          history: response.data.metrics_history,
          summary: response.data.summary
        };
      } else {
        throw new SimulationError('Failed to get metrics from engine');
      }
    } catch (error) {
      if (error instanceof NotFoundError || error instanceof SimulationError) {
        throw error;
      }
      logger.error(`Failed to get metrics for simulation ${simulationId}:`, error);
      throw new AppError('Failed to retrieve simulation metrics', 500);
    }
  }

  /**
   * Call simulation engine API
   */
  async callSimulationEngine(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${this.simulationEngineUrl}${endpoint}`,
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      logger.error(`Simulation engine API call failed: ${method} ${endpoint}`, error);
      
      if (error.code === 'ECONNREFUSED') {
        throw new ExternalServiceError('Simulation engine is not available');
      }
      
      if (error.response) {
        throw new ExternalServiceError(`Simulation engine error: ${error.response.data?.message || error.response.statusText}`);
      }
      
      throw new ExternalServiceError('Failed to communicate with simulation engine');
    }
  }

  /**
   * Generate unique ID
   */
  generateId() {
    return `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

module.exports = new SimulationService();
