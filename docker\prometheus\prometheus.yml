# ManufactureX Prometheus Configuration
# Monitoring and metrics collection configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'manufacturex-monitor'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # ManufactureX Backend API
  - job_name: 'manufacturex-backend'
    static_configs:
      - targets: ['backend:5000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # ManufactureX Simulation Engine
  - job_name: 'manufacturex-simulation-engine'
    static_configs:
      - targets: ['simulation-engine:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # InfluxDB
  - job_name: 'influxdb'
    static_configs:
      - targets: ['influxdb:8086']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # MQTT Broker (Mosquitto)
  - job_name: 'mosquitto'
    static_configs:
      - targets: ['mqtt:1883']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx Reverse Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s
    metrics_path: /nginx_status
    scrape_timeout: 10s

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # cAdvisor (Container Metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Custom Manufacturing Metrics
  - job_name: 'manufacturing-metrics'
    static_configs:
      - targets: ['simulation-engine:8000']
    scrape_interval: 5s
    metrics_path: /manufacturing/metrics
    scrape_timeout: 5s
    params:
      format: ['prometheus']

  # Blackbox Exporter (Endpoint Monitoring)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://frontend:3000
        - http://backend:5000/health
        - http://simulation-engine:8000/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "http://influxdb:8086/api/v1/prom/write?db=prometheus"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# Remote read configuration
# remote_read:
#   - url: "http://influxdb:8086/api/v1/prom/read?db=prometheus"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
    path: /prometheus
    wal-compression: true

# Web configuration
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true
  max-connections: 512
  read-timeout: 30s
