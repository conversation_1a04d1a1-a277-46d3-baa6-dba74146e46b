"""
Logging Configuration
Structured logging setup for the simulation engine
"""

import logging
import logging.handlers
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional
import structlog

def setup_logger(name: str, config: Optional[Dict[str, Any]] = None) -> structlog.stdlib.BoundLogger:
    """
    Setup structured logger with proper configuration
    
    Args:
        name: Logger name
        config: Configuration dictionary
    
    Returns:
        Configured structlog logger
    """
    
    # Default configuration
    default_config = {
        'level': 'INFO',
        'format': 'json',
        'file_path': './logs/simulation.log',
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        'console_output': True
    }
    
    if config:
        default_config.update(config)
    
    # Create logs directory
    log_file = Path(default_config['file_path'])
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, default_config['level'].upper())
    )
    
    # Setup file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=default_config['file_path'],
        maxBytes=default_config['max_bytes'],
        backupCount=default_config['backup_count'],
        encoding='utf-8'
    )
    
    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    
    # Configure formatters
    if default_config['format'] == 'json':
        formatter = JsonFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Get root logger and add handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.addHandler(file_handler)
    
    if default_config['console_output']:
        root_logger.addHandler(console_handler)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if default_config['format'] == 'json' else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger(name)

class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for log records"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        # Base log data
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_data[key] = value
        
        return json.dumps(log_data, default=str, ensure_ascii=False)

class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger):
        self.logger = logger
    
    def log_execution_time(self, operation: str, duration: float, **kwargs):
        """Log execution time for an operation"""
        self.logger.info(
            "Performance metric",
            operation=operation,
            duration_ms=round(duration * 1000, 2),
            **kwargs
        )
    
    def log_memory_usage(self, operation: str, memory_mb: float, **kwargs):
        """Log memory usage for an operation"""
        self.logger.info(
            "Memory usage",
            operation=operation,
            memory_mb=round(memory_mb, 2),
            **kwargs
        )
    
    def log_throughput(self, operation: str, items_per_second: float, **kwargs):
        """Log throughput metrics"""
        self.logger.info(
            "Throughput metric",
            operation=operation,
            items_per_second=round(items_per_second, 2),
            **kwargs
        )

class SecurityLogger:
    """Logger for security events"""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger):
        self.logger = logger
    
    def log_authentication_attempt(self, success: bool, user_id: Optional[str] = None, 
                                 ip_address: Optional[str] = None, **kwargs):
        """Log authentication attempts"""
        self.logger.warning(
            "Authentication attempt",
            success=success,
            user_id=user_id,
            ip_address=ip_address,
            **kwargs
        )
    
    def log_authorization_failure(self, user_id: str, resource: str, 
                                action: str, **kwargs):
        """Log authorization failures"""
        self.logger.warning(
            "Authorization failure",
            user_id=user_id,
            resource=resource,
            action=action,
            **kwargs
        )
    
    def log_suspicious_activity(self, activity_type: str, details: Dict[str, Any], **kwargs):
        """Log suspicious activities"""
        self.logger.error(
            "Suspicious activity detected",
            activity_type=activity_type,
            details=details,
            **kwargs
        )

class BusinessLogger:
    """Logger for business events"""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger):
        self.logger = logger
    
    def log_simulation_event(self, event_type: str, simulation_id: str, **kwargs):
        """Log simulation-related events"""
        self.logger.info(
            "Simulation event",
            event_type=event_type,
            simulation_id=simulation_id,
            **kwargs
        )
    
    def log_optimization_event(self, event_type: str, optimization_id: str, **kwargs):
        """Log optimization-related events"""
        self.logger.info(
            "Optimization event",
            event_type=event_type,
            optimization_id=optimization_id,
            **kwargs
        )
    
    def log_digital_twin_event(self, event_type: str, twin_id: str, **kwargs):
        """Log digital twin events"""
        self.logger.info(
            "Digital twin event",
            event_type=event_type,
            twin_id=twin_id,
            **kwargs
        )

class ErrorLogger:
    """Logger for error tracking"""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger):
        self.logger = logger
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None, **kwargs):
        """Log errors with context"""
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc()
        }
        
        if context:
            error_data['context'] = context
        
        self.logger.error(
            "Application error",
            **error_data,
            **kwargs
        )
    
    def log_validation_error(self, field: str, value: Any, message: str, **kwargs):
        """Log validation errors"""
        self.logger.warning(
            "Validation error",
            field=field,
            value=value,
            message=message,
            **kwargs
        )
    
    def log_external_service_error(self, service: str, error: Exception, **kwargs):
        """Log external service errors"""
        self.logger.error(
            "External service error",
            service=service,
            error_type=type(error).__name__,
            error_message=str(error),
            **kwargs
        )

# Convenience function to get all logger types
def get_loggers(name: str, config: Optional[Dict[str, Any]] = None):
    """Get all logger types for a module"""
    base_logger = setup_logger(name, config)
    
    return {
        'logger': base_logger,
        'performance': PerformanceLogger(base_logger),
        'security': SecurityLogger(base_logger),
        'business': BusinessLogger(base_logger),
        'error': ErrorLogger(base_logger)
    }

# Module-level logger setup
_loggers = get_loggers(__name__)
logger = _loggers['logger']
performance_logger = _loggers['performance']
security_logger = _loggers['security']
business_logger = _loggers['business']
error_logger = _loggers['error']
