/**
 * Sidebar Navigation Component
 * Main navigation sidebar for the application
 */

import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  PlayIcon,
  CogIcon,
  ChartBarIcon,
  BeakerIcon,
  CircleStackIcon,
  Cog6ToothIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

// Logo component
const Logo = () => (
  <div className="flex items-center space-x-3">
    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
      <CogIcon className="h-5 w-5 text-white" />
    </div>
    <span className="text-xl font-bold text-gray-900 dark:text-white">
      ManufactureX
    </span>
  </div>
);

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      description: 'Overview and real-time monitoring'
    },
    {
      name: 'Simulations',
      href: '/simulations',
      icon: PlayIcon,
      description: 'Manage manufacturing simulations'
    },
    {
      name: 'Digital Twins',
      href: '/digital-twins',
      icon: CogIcon,
      description: 'Digital twin management'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      description: 'Performance analytics and insights'
    },
    {
      name: 'Optimization',
      href: '/optimization',
      icon: BeakerIcon,
      description: 'Process optimization tools'
    },
    {
      name: 'Data Sources',
      href: '/data-sources',
      icon: CircleStackIcon,
      description: 'Data collection and management'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      description: 'Application settings'
    }
  ];

  const isActive = (href) => {
    if (href === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(href);
  };

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      opacity: 0,
      x: -20
    }
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4 dark:border-gray-700 dark:bg-gray-800">
          {/* Logo */}
          <div className="flex h-16 shrink-0 items-center">
            <Logo />
          </div>

          {/* Navigation */}
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      <NavLink
                        to={item.href}
                        className={({ isActive: navIsActive }) =>
                          `group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors duration-200 ${
                            isActive(item.href) || navIsActive
                              ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                              : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-blue-400 dark:hover:bg-gray-700'
                          }`
                        }
                        title={item.description}
                      >
                        <item.icon
                          className={`h-6 w-6 shrink-0 transition-colors duration-200 ${
                            isActive(item.href)
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                          }`}
                          aria-hidden="true"
                        />
                        {item.name}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>

              {/* Bottom section */}
              <li className="mt-auto">
                <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                      <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        System Status
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        All systems operational
                      </p>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <motion.div
        className="fixed inset-y-0 z-50 flex w-64 flex-col lg:hidden"
        variants={sidebarVariants}
        initial="closed"
        animate={isOpen ? "open" : "closed"}
      >
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 dark:bg-gray-800">
          {/* Header with close button */}
          <div className="flex h-16 shrink-0 items-center justify-between">
            <Logo />
            <button
              type="button"
              className="-m-2.5 p-2.5 text-gray-700 dark:text-gray-200"
              onClick={onClose}
            >
              <span className="sr-only">Close sidebar</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item, index) => (
                    <motion.li
                      key={item.name}
                      variants={itemVariants}
                      initial="closed"
                      animate={isOpen ? "open" : "closed"}
                      transition={{ delay: index * 0.1 }}
                    >
                      <NavLink
                        to={item.href}
                        onClick={onClose}
                        className={({ isActive: navIsActive }) =>
                          `group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors duration-200 ${
                            isActive(item.href) || navIsActive
                              ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                              : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-blue-400 dark:hover:bg-gray-700'
                          }`
                        }
                        title={item.description}
                      >
                        <item.icon
                          className={`h-6 w-6 shrink-0 transition-colors duration-200 ${
                            isActive(item.href)
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                          }`}
                          aria-hidden="true"
                        />
                        {item.name}
                      </NavLink>
                    </motion.li>
                  ))}
                </ul>
              </li>

              {/* Bottom section */}
              <motion.li
                className="mt-auto"
                variants={itemVariants}
                initial="closed"
                animate={isOpen ? "open" : "closed"}
                transition={{ delay: navigation.length * 0.1 }}
              >
                <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                      <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        System Status
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        All systems operational
                      </p>
                    </div>
                  </div>
                </div>
              </motion.li>
            </ul>
          </nav>
        </div>
      </motion.div>
    </>
  );
};

export default Sidebar;
