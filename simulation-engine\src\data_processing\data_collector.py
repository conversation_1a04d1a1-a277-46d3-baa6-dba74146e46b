"""
Data Collector
Collects and processes data from various sources for manufacturing simulation
"""

import asyncio
import logging
import time
import json
import random
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aioredis
import asyncio_mqtt as aiomqtt

logger = logging.getLogger(__name__)

class DataSourceType(Enum):
    """Types of data sources"""
    MQTT_SENSOR = "mqtt_sensor"
    REST_API = "rest_api"
    DATABASE = "database"
    FILE_SYSTEM = "file_system"
    SIMULATION = "simulation"
    MANUAL_INPUT = "manual_input"

class DataType(Enum):
    """Types of data"""
    SENSOR_READING = "sensor_reading"
    MACHINE_STATUS = "machine_status"
    PRODUCTION_COUNT = "production_count"
    QUALITY_METRIC = "quality_metric"
    ALARM = "alarm"
    MAINTENANCE_LOG = "maintenance_log"
    OPERATOR_INPUT = "operator_input"

@dataclass
class DataPoint:
    """Individual data point"""
    source_id: str
    data_type: DataType
    timestamp: float
    value: Any
    unit: Optional[str] = None
    quality: float = 1.0  # Data quality score 0-1
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class DataSource:
    """Data source configuration"""
    source_id: str
    source_type: DataSourceType
    name: str
    description: str
    connection_config: Dict[str, Any]
    data_types: List[DataType]
    collection_frequency: float = 1.0  # seconds
    enabled: bool = True
    
class DataCollector:
    """Collects data from multiple sources for manufacturing simulation"""
    
    def __init__(self, config):
        self.config = config
        self.data_sources: Dict[str, DataSource] = {}
        self.collection_tasks: Dict[str, asyncio.Task] = {}
        self.data_buffer: List[DataPoint] = []
        self.data_callbacks: List[Callable] = []
        
        # Storage connections
        self.redis_client: Optional[aioredis.Redis] = None
        self.mqtt_client: Optional[aiomqtt.Client] = None
        
        # Statistics
        self.collection_stats = {
            'total_points_collected': 0,
            'points_per_source': {},
            'last_collection_time': {},
            'error_count': 0
        }
        
        self._running = False
    
    async def start(self):
        """Start data collection"""
        try:
            logger.info("Starting Data Collector...")
            
            # Initialize connections
            await self._initialize_connections()
            
            # Initialize default data sources
            await self._initialize_default_sources()
            
            # Start collection tasks
            for source_id in self.data_sources:
                await self._start_source_collection(source_id)
            
            # Start data processing task
            asyncio.create_task(self._process_data_buffer())
            
            self._running = True
            logger.info("Data Collector started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Data Collector: {e}")
            raise
    
    async def stop(self):
        """Stop data collection"""
        try:
            logger.info("Stopping Data Collector...")
            
            self._running = False
            
            # Stop collection tasks
            for source_id in list(self.collection_tasks.keys()):
                await self._stop_source_collection(source_id)
            
            # Close connections
            if self.redis_client:
                await self.redis_client.close()
            
            if self.mqtt_client:
                await self.mqtt_client.disconnect()
            
            logger.info("Data Collector stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Data Collector: {e}")
    
    def is_running(self) -> bool:
        """Check if data collector is running"""
        return self._running
    
    async def _initialize_connections(self):
        """Initialize external connections"""
        try:
            # Initialize Redis connection
            redis_url = self.config.get('REDIS_URL', 'redis://localhost:6379')
            self.redis_client = aioredis.from_url(redis_url)
            
            # Test Redis connection
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Initialize MQTT connection (optional)
            mqtt_config = self.config.get('MQTT_CONFIG', {})
            if mqtt_config.get('enabled', False):
                self.mqtt_client = aiomqtt.Client(
                    hostname=mqtt_config.get('host', 'localhost'),
                    port=mqtt_config.get('port', 1883),
                    username=mqtt_config.get('username'),
                    password=mqtt_config.get('password')
                )
                await self.mqtt_client.connect()
                logger.info("MQTT connection established")
            
        except Exception as e:
            logger.error(f"Failed to initialize connections: {e}")
            raise
    
    async def _initialize_default_sources(self):
        """Initialize default data sources"""
        # Simulated machine sensors
        machine_sensors = [
            {
                'source_id': 'machine_01_temp',
                'source_type': DataSourceType.SIMULATION,
                'name': 'Machine 01 Temperature',
                'description': 'Temperature sensor for CNC Machine 01',
                'data_types': [DataType.SENSOR_READING],
                'connection_config': {'sensor_type': 'temperature', 'range': [20, 80]},
                'collection_frequency': 2.0
            },
            {
                'source_id': 'machine_01_vibration',
                'source_type': DataSourceType.SIMULATION,
                'name': 'Machine 01 Vibration',
                'description': 'Vibration sensor for CNC Machine 01',
                'data_types': [DataType.SENSOR_READING],
                'connection_config': {'sensor_type': 'vibration', 'range': [0, 5]},
                'collection_frequency': 1.0
            },
            {
                'source_id': 'production_counter',
                'source_type': DataSourceType.SIMULATION,
                'name': 'Production Counter',
                'description': 'Counts produced units',
                'data_types': [DataType.PRODUCTION_COUNT],
                'connection_config': {'counter_type': 'incremental'},
                'collection_frequency': 5.0
            },
            {
                'source_id': 'quality_inspector',
                'source_type': DataSourceType.SIMULATION,
                'name': 'Quality Inspector',
                'description': 'Quality inspection results',
                'data_types': [DataType.QUALITY_METRIC],
                'connection_config': {'inspection_type': 'automated'},
                'collection_frequency': 10.0
            }
        ]
        
        for source_config in machine_sensors:
            await self.add_data_source(source_config)
    
    async def add_data_source(self, source_config: Dict[str, Any]):
        """Add a new data source"""
        try:
            source = DataSource(
                source_id=source_config['source_id'],
                source_type=DataSourceType(source_config['source_type']),
                name=source_config['name'],
                description=source_config['description'],
                connection_config=source_config['connection_config'],
                data_types=[DataType(dt) for dt in source_config['data_types']],
                collection_frequency=source_config.get('collection_frequency', 1.0),
                enabled=source_config.get('enabled', True)
            )
            
            self.data_sources[source.source_id] = source
            
            # Start collection if collector is running
            if self._running and source.enabled:
                await self._start_source_collection(source.source_id)
            
            logger.info(f"Added data source: {source.source_id}")
            
        except Exception as e:
            logger.error(f"Failed to add data source: {e}")
            raise
    
    async def _start_source_collection(self, source_id: str):
        """Start collection for a specific data source"""
        if source_id not in self.data_sources:
            return
        
        source = self.data_sources[source_id]
        if not source.enabled:
            return
        
        # Create collection task
        task = asyncio.create_task(self._collect_from_source(source))
        self.collection_tasks[source_id] = task
        
        logger.info(f"Started collection for source: {source_id}")
    
    async def _stop_source_collection(self, source_id: str):
        """Stop collection for a specific data source"""
        if source_id in self.collection_tasks:
            task = self.collection_tasks[source_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            del self.collection_tasks[source_id]
            
            logger.info(f"Stopped collection for source: {source_id}")
    
    async def _collect_from_source(self, source: DataSource):
        """Collect data from a specific source"""
        try:
            while self._running and source.enabled:
                try:
                    # Collect data based on source type
                    if source.source_type == DataSourceType.SIMULATION:
                        data_points = await self._collect_simulated_data(source)
                    elif source.source_type == DataSourceType.MQTT_SENSOR:
                        data_points = await self._collect_mqtt_data(source)
                    elif source.source_type == DataSourceType.REST_API:
                        data_points = await self._collect_api_data(source)
                    else:
                        data_points = []
                    
                    # Add to buffer
                    for data_point in data_points:
                        await self._add_data_point(data_point)
                    
                    # Update statistics
                    self.collection_stats['points_per_source'][source.source_id] = \
                        self.collection_stats['points_per_source'].get(source.source_id, 0) + len(data_points)
                    self.collection_stats['last_collection_time'][source.source_id] = time.time()
                    
                except Exception as e:
                    logger.error(f"Error collecting from source {source.source_id}: {e}")
                    self.collection_stats['error_count'] += 1
                
                # Wait for next collection
                await asyncio.sleep(source.collection_frequency)
                
        except asyncio.CancelledError:
            logger.info(f"Collection cancelled for source: {source.source_id}")
            raise
        except Exception as e:
            logger.error(f"Collection failed for source {source.source_id}: {e}")
    
    async def _collect_simulated_data(self, source: DataSource) -> List[DataPoint]:
        """Collect simulated data"""
        data_points = []
        current_time = time.time()
        
        config = source.connection_config
        
        for data_type in source.data_types:
            if data_type == DataType.SENSOR_READING:
                # Generate sensor reading
                sensor_type = config.get('sensor_type', 'generic')
                value_range = config.get('range', [0, 100])
                
                if sensor_type == 'temperature':
                    # Temperature with some drift
                    base_temp = (value_range[0] + value_range[1]) / 2
                    value = base_temp + random.gauss(0, 5) + math.sin(current_time / 100) * 3
                    value = max(value_range[0], min(value_range[1], value))
                    unit = '°C'
                elif sensor_type == 'vibration':
                    # Vibration with occasional spikes
                    base_vibration = value_range[0] + (value_range[1] - value_range[0]) * 0.2
                    spike = random.random() < 0.05  # 5% chance of spike
                    value = base_vibration + random.uniform(0, 0.5) + (2.0 if spike else 0)
                    value = max(value_range[0], min(value_range[1], value))
                    unit = 'mm/s'
                else:
                    value = random.uniform(value_range[0], value_range[1])
                    unit = None
                
                data_point = DataPoint(
                    source_id=source.source_id,
                    data_type=data_type,
                    timestamp=current_time,
                    value=value,
                    unit=unit,
                    quality=random.uniform(0.95, 1.0),
                    metadata={'sensor_type': sensor_type}
                )
                data_points.append(data_point)
                
            elif data_type == DataType.PRODUCTION_COUNT:
                # Production counter
                counter_type = config.get('counter_type', 'incremental')
                if counter_type == 'incremental':
                    # Random production between 0-5 units per collection
                    value = random.randint(0, 5)
                else:
                    value = random.randint(50, 150)  # Total count
                
                data_point = DataPoint(
                    source_id=source.source_id,
                    data_type=data_type,
                    timestamp=current_time,
                    value=value,
                    unit='units',
                    quality=1.0,
                    metadata={'counter_type': counter_type}
                )
                data_points.append(data_point)
                
            elif data_type == DataType.QUALITY_METRIC:
                # Quality inspection result
                # 95% chance of pass, 5% chance of fail
                passed = random.random() > 0.05
                value = 1 if passed else 0
                
                data_point = DataPoint(
                    source_id=source.source_id,
                    data_type=data_type,
                    timestamp=current_time,
                    value=value,
                    unit='pass/fail',
                    quality=1.0,
                    metadata={'inspection_result': 'pass' if passed else 'fail'}
                )
                data_points.append(data_point)
        
        return data_points
    
    async def _collect_mqtt_data(self, source: DataSource) -> List[DataPoint]:
        """Collect data from MQTT source"""
        # Placeholder for MQTT data collection
        # In practice, this would subscribe to MQTT topics and parse messages
        return []
    
    async def _collect_api_data(self, source: DataSource) -> List[DataPoint]:
        """Collect data from REST API source"""
        # Placeholder for API data collection
        # In practice, this would make HTTP requests to external APIs
        return []
    
    async def _add_data_point(self, data_point: DataPoint):
        """Add data point to buffer and storage"""
        # Add to buffer
        self.data_buffer.append(data_point)
        
        # Update statistics
        self.collection_stats['total_points_collected'] += 1
        
        # Store in Redis for real-time access
        if self.redis_client:
            try:
                key = f"data:{data_point.source_id}:latest"
                value = json.dumps(asdict(data_point), default=str)
                await self.redis_client.set(key, value, ex=3600)  # Expire in 1 hour
            except Exception as e:
                logger.error(f"Failed to store data point in Redis: {e}")
        
        # Notify callbacks
        for callback in self.data_callbacks:
            try:
                await callback(data_point)
            except Exception as e:
                logger.error(f"Data callback failed: {e}")
    
    async def _process_data_buffer(self):
        """Process data buffer periodically"""
        while self._running:
            try:
                if len(self.data_buffer) > 1000:  # Process when buffer gets large
                    # Process batch of data points
                    batch = self.data_buffer[:500]
                    self.data_buffer = self.data_buffer[500:]
                    
                    await self._process_data_batch(batch)
                
                await asyncio.sleep(10)  # Process every 10 seconds
                
            except Exception as e:
                logger.error(f"Data buffer processing failed: {e}")
    
    async def _process_data_batch(self, batch: List[DataPoint]):
        """Process a batch of data points"""
        try:
            # Group by source and data type
            grouped_data = {}
            for data_point in batch:
                key = (data_point.source_id, data_point.data_type)
                if key not in grouped_data:
                    grouped_data[key] = []
                grouped_data[key].append(data_point)
            
            # Perform aggregations and analysis
            for (source_id, data_type), points in grouped_data.items():
                await self._analyze_data_group(source_id, data_type, points)
            
        except Exception as e:
            logger.error(f"Data batch processing failed: {e}")
    
    async def _analyze_data_group(self, source_id: str, data_type: DataType, points: List[DataPoint]):
        """Analyze a group of data points"""
        try:
            if not points:
                return
            
            # Calculate basic statistics
            values = [p.value for p in points if isinstance(p.value, (int, float))]
            
            if values:
                stats = {
                    'count': len(values),
                    'min': min(values),
                    'max': max(values),
                    'avg': sum(values) / len(values),
                    'latest': values[-1],
                    'timestamp': points[-1].timestamp
                }
                
                # Store aggregated stats
                if self.redis_client:
                    key = f"stats:{source_id}:{data_type.value}"
                    value = json.dumps(stats, default=str)
                    await self.redis_client.set(key, value, ex=7200)  # Expire in 2 hours
            
        except Exception as e:
            logger.error(f"Data analysis failed for {source_id}:{data_type}: {e}")
    
    def add_data_callback(self, callback: Callable):
        """Add callback for real-time data processing"""
        self.data_callbacks.append(callback)
    
    def remove_data_callback(self, callback: Callable):
        """Remove data callback"""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)
    
    async def get_latest_data(self, source_id: str) -> Optional[DataPoint]:
        """Get latest data point for a source"""
        if self.redis_client:
            try:
                key = f"data:{source_id}:latest"
                value = await self.redis_client.get(key)
                if value:
                    data_dict = json.loads(value)
                    data_dict['data_type'] = DataType(data_dict['data_type'])
                    return DataPoint(**data_dict)
            except Exception as e:
                logger.error(f"Failed to get latest data for {source_id}: {e}")
        
        return None
    
    async def get_data_statistics(self) -> Dict[str, Any]:
        """Get data collection statistics"""
        return self.collection_stats.copy()
    
    async def get_source_list(self) -> List[Dict[str, Any]]:
        """Get list of configured data sources"""
        sources = []
        for source in self.data_sources.values():
            sources.append({
                'source_id': source.source_id,
                'name': source.name,
                'type': source.source_type.value,
                'enabled': source.enabled,
                'frequency': source.collection_frequency,
                'data_types': [dt.value for dt in source.data_types]
            })
        return sources

# Import math for simulated data generation
import math
