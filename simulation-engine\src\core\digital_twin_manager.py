"""
Digital Twin Manager
Manages digital twins of manufacturing processes and equipment
"""

import asyncio
import uuid
import time
import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from ..utils.exceptions import DigitalTwinError, ValidationError

logger = logging.getLogger(__name__)

class TwinType(Enum):
    """Digital twin types"""
    MACHINE = "machine"
    PROCESS = "process"
    FACTORY = "factory"
    PRODUCT = "product"
    SYSTEM = "system"

class TwinStatus(Enum):
    """Digital twin status"""
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    ARCHIVED = "archived"

@dataclass
class TwinConfiguration:
    """Digital twin configuration"""
    name: str
    twin_type: TwinType
    description: str
    physical_asset_id: Optional[str] = None
    parameters: Dict[str, Any] = None
    sensors: List[Dict[str, Any]] = None
    actuators: List[Dict[str, Any]] = None
    update_frequency: float = 1.0  # seconds
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.sensors is None:
            self.sensors = []
        if self.actuators is None:
            self.actuators = []
    
    def validate(self):
        """Validate twin configuration"""
        if not self.name or not isinstance(self.name, str):
            raise ValidationError("Twin name is required and must be a string")
        
        if not isinstance(self.twin_type, TwinType):
            raise ValidationError("Invalid twin type")
        
        if self.update_frequency <= 0:
            raise ValidationError("Update frequency must be positive")

@dataclass
class DigitalTwin:
    """Digital twin representation"""
    twin_id: str
    config: TwinConfiguration
    status: TwinStatus
    created_at: float
    last_updated: float
    current_state: Dict[str, Any] = None
    historical_data: List[Dict[str, Any]] = None
    predictions: Dict[str, Any] = None
    anomalies: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.current_state is None:
            self.current_state = {}
        if self.historical_data is None:
            self.historical_data = []
        if self.predictions is None:
            self.predictions = {}
        if self.anomalies is None:
            self.anomalies = []

class DigitalTwinManager:
    """Manages digital twins for manufacturing systems"""
    
    def __init__(self, config):
        self.config = config
        self.twins: Dict[str, DigitalTwin] = {}
        self.update_tasks: Dict[str, asyncio.Task] = {}
        self._initialized = False
        
        # Twin type handlers
        self.twin_handlers = {
            TwinType.MACHINE: self._handle_machine_twin,
            TwinType.PROCESS: self._handle_process_twin,
            TwinType.FACTORY: self._handle_factory_twin,
            TwinType.PRODUCT: self._handle_product_twin,
            TwinType.SYSTEM: self._handle_system_twin
        }
    
    async def initialize(self):
        """Initialize the digital twin manager"""
        try:
            logger.info("Initializing Digital Twin Manager...")
            
            # Initialize any required resources
            # Could include database connections, IoT connections, etc.
            
            self._initialized = True
            logger.info("Digital Twin Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Digital Twin Manager: {e}")
            raise DigitalTwinError(f"Initialization failed: {e}")
    
    async def shutdown(self):
        """Shutdown the digital twin manager"""
        try:
            logger.info("Shutting down Digital Twin Manager...")
            
            # Stop all update tasks
            for twin_id in list(self.update_tasks.keys()):
                await self._stop_twin_updates(twin_id)
            
            # Clean up resources
            self.twins.clear()
            self.update_tasks.clear()
            
            self._initialized = False
            logger.info("Digital Twin Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def is_healthy(self) -> bool:
        """Check if the digital twin manager is healthy"""
        return self._initialized
    
    async def create_twin(self, config_dict: Dict[str, Any]) -> str:
        """Create a new digital twin"""
        try:
            # Parse twin type
            twin_type_str = config_dict.get('twin_type', '').lower()
            twin_type = TwinType(twin_type_str)
            config_dict['twin_type'] = twin_type
            
            # Create and validate configuration
            config = TwinConfiguration(**config_dict)
            config.validate()
            
            # Generate unique twin ID
            twin_id = str(uuid.uuid4())
            
            # Create digital twin
            twin = DigitalTwin(
                twin_id=twin_id,
                config=config,
                status=TwinStatus.CREATED,
                created_at=time.time(),
                last_updated=time.time()
            )
            
            # Initialize twin based on type
            await self._initialize_twin(twin)
            
            # Store twin
            self.twins[twin_id] = twin
            
            # Start update task
            await self._start_twin_updates(twin_id)
            
            logger.info(f"Created digital twin {twin_id}: {config.name}")
            return twin_id
            
        except ValidationError:
            raise
        except ValueError as e:
            raise ValidationError(f"Invalid twin type: {e}")
        except Exception as e:
            logger.error(f"Failed to create digital twin: {e}")
            raise DigitalTwinError(f"Failed to create digital twin: {e}")
    
    async def _initialize_twin(self, twin: DigitalTwin):
        """Initialize a digital twin based on its type"""
        try:
            twin.status = TwinStatus.INITIALIZING
            
            # Get type-specific handler
            handler = self.twin_handlers.get(twin.config.twin_type)
            if handler:
                await handler(twin, 'initialize')
            
            # Set initial state
            twin.current_state = await self._get_initial_state(twin)
            twin.status = TwinStatus.ACTIVE
            
        except Exception as e:
            twin.status = TwinStatus.ERROR
            logger.error(f"Failed to initialize twin {twin.twin_id}: {e}")
            raise
    
    async def _get_initial_state(self, twin: DigitalTwin) -> Dict[str, Any]:
        """Get initial state for a digital twin"""
        if twin.config.twin_type == TwinType.MACHINE:
            return {
                'status': 'idle',
                'temperature': 25.0,
                'vibration': 0.1,
                'speed': 0.0,
                'efficiency': 100.0,
                'runtime_hours': 0.0,
                'maintenance_due': False
            }
        elif twin.config.twin_type == TwinType.PROCESS:
            return {
                'status': 'ready',
                'throughput': 0.0,
                'quality_rate': 100.0,
                'cycle_time': twin.config.parameters.get('cycle_time', 60.0),
                'queue_length': 0,
                'efficiency': 100.0
            }
        elif twin.config.twin_type == TwinType.FACTORY:
            return {
                'status': 'operational',
                'overall_efficiency': 100.0,
                'total_throughput': 0.0,
                'energy_consumption': 0.0,
                'active_machines': 0,
                'total_machines': twin.config.parameters.get('total_machines', 10)
            }
        elif twin.config.twin_type == TwinType.PRODUCT:
            return {
                'status': 'in_design',
                'quality_score': 100.0,
                'cost_estimate': twin.config.parameters.get('base_cost', 100.0),
                'production_time': twin.config.parameters.get('production_time', 3600.0),
                'materials_required': twin.config.parameters.get('materials', [])
            }
        else:  # SYSTEM
            return {
                'status': 'online',
                'performance': 100.0,
                'availability': 100.0,
                'reliability': 100.0,
                'maintainability': 100.0
            }
    
    async def _start_twin_updates(self, twin_id: str):
        """Start periodic updates for a digital twin"""
        if twin_id not in self.twins:
            return
        
        twin = self.twins[twin_id]
        task = asyncio.create_task(self._update_twin_loop(twin_id))
        self.update_tasks[twin_id] = task
        
        logger.info(f"Started updates for twin {twin_id}")
    
    async def _stop_twin_updates(self, twin_id: str):
        """Stop periodic updates for a digital twin"""
        if twin_id in self.update_tasks:
            task = self.update_tasks[twin_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            del self.update_tasks[twin_id]
            
            logger.info(f"Stopped updates for twin {twin_id}")
    
    async def _update_twin_loop(self, twin_id: str):
        """Periodic update loop for a digital twin"""
        try:
            twin = self.twins[twin_id]
            
            while twin.status == TwinStatus.ACTIVE:
                # Update twin state
                await self._update_twin_state(twin)
                
                # Check for anomalies
                await self._detect_anomalies(twin)
                
                # Generate predictions
                await self._generate_predictions(twin)
                
                # Store historical data
                await self._store_historical_data(twin)
                
                # Wait for next update
                await asyncio.sleep(twin.config.update_frequency)
                
        except asyncio.CancelledError:
            logger.info(f"Update loop cancelled for twin {twin_id}")
            raise
        except Exception as e:
            logger.error(f"Update loop failed for twin {twin_id}: {e}")
            if twin_id in self.twins:
                self.twins[twin_id].status = TwinStatus.ERROR
    
    async def _update_twin_state(self, twin: DigitalTwin):
        """Update the state of a digital twin"""
        try:
            # Get type-specific handler
            handler = self.twin_handlers.get(twin.config.twin_type)
            if handler:
                new_state = await handler(twin, 'update')
                if new_state:
                    twin.current_state.update(new_state)
            
            twin.last_updated = time.time()
            
        except Exception as e:
            logger.error(f"Failed to update twin state {twin.twin_id}: {e}")
    
    async def _handle_machine_twin(self, twin: DigitalTwin, action: str) -> Optional[Dict[str, Any]]:
        """Handle machine digital twin operations"""
        if action == 'initialize':
            # Initialize machine-specific parameters
            twin.config.parameters.setdefault('max_temperature', 80.0)
            twin.config.parameters.setdefault('max_vibration', 2.0)
            twin.config.parameters.setdefault('rated_speed', 1800.0)
            
        elif action == 'update':
            # Simulate machine state changes
            current_state = twin.current_state
            
            # Simulate temperature changes
            temp_change = random.uniform(-1.0, 2.0)
            new_temp = max(20.0, min(100.0, current_state.get('temperature', 25.0) + temp_change))
            
            # Simulate vibration
            base_vibration = 0.1 if current_state.get('status') == 'idle' else 0.5
            vibration = base_vibration + random.uniform(-0.1, 0.3)
            
            # Simulate efficiency degradation
            efficiency = current_state.get('efficiency', 100.0)
            if efficiency > 70:
                efficiency -= random.uniform(0, 0.1)
            
            return {
                'temperature': new_temp,
                'vibration': max(0, vibration),
                'efficiency': max(0, efficiency),
                'runtime_hours': current_state.get('runtime_hours', 0) + (twin.config.update_frequency / 3600),
                'maintenance_due': new_temp > 85 or vibration > 2.0 or efficiency < 75
            }
        
        return None
    
    async def _handle_process_twin(self, twin: DigitalTwin, action: str) -> Optional[Dict[str, Any]]:
        """Handle process digital twin operations"""
        if action == 'update':
            current_state = twin.current_state
            
            # Simulate process metrics
            throughput_change = random.uniform(-5.0, 10.0)
            new_throughput = max(0, current_state.get('throughput', 0) + throughput_change)
            
            quality_change = random.uniform(-0.5, 0.2)
            new_quality = max(80, min(100, current_state.get('quality_rate', 100) + quality_change))
            
            return {
                'throughput': new_throughput,
                'quality_rate': new_quality,
                'queue_length': max(0, current_state.get('queue_length', 0) + random.randint(-2, 3)),
                'efficiency': (new_quality / 100) * min(100, new_throughput / 50) if new_throughput > 0 else 0
            }
        
        return None
    
    async def _handle_factory_twin(self, twin: DigitalTwin, action: str) -> Optional[Dict[str, Any]]:
        """Handle factory digital twin operations"""
        if action == 'update':
            current_state = twin.current_state
            total_machines = current_state.get('total_machines', 10)
            
            # Simulate factory-level metrics
            active_change = random.randint(-1, 2)
            new_active = max(0, min(total_machines, current_state.get('active_machines', 0) + active_change))
            
            efficiency = (new_active / total_machines) * 100 if total_machines > 0 else 0
            throughput = new_active * random.uniform(80, 120)
            energy = new_active * random.uniform(50, 80)
            
            return {
                'active_machines': new_active,
                'overall_efficiency': efficiency,
                'total_throughput': throughput,
                'energy_consumption': energy
            }
        
        return None
    
    async def _handle_product_twin(self, twin: DigitalTwin, action: str) -> Optional[Dict[str, Any]]:
        """Handle product digital twin operations"""
        if action == 'update':
            current_state = twin.current_state
            
            # Simulate product development metrics
            quality_change = random.uniform(-0.1, 0.5)
            new_quality = max(70, min(100, current_state.get('quality_score', 100) + quality_change))
            
            cost_change = random.uniform(-5.0, 10.0)
            new_cost = max(50, current_state.get('cost_estimate', 100) + cost_change)
            
            return {
                'quality_score': new_quality,
                'cost_estimate': new_cost,
                'production_time': current_state.get('production_time', 3600) + random.uniform(-60, 120)
            }
        
        return None
    
    async def _handle_system_twin(self, twin: DigitalTwin, action: str) -> Optional[Dict[str, Any]]:
        """Handle system digital twin operations"""
        if action == 'update':
            current_state = twin.current_state
            
            # Simulate system performance metrics
            perf_change = random.uniform(-1.0, 0.5)
            new_performance = max(70, min(100, current_state.get('performance', 100) + perf_change))
            
            return {
                'performance': new_performance,
                'availability': max(90, min(100, new_performance + random.uniform(-2, 2))),
                'reliability': max(85, min(100, new_performance + random.uniform(-3, 1))),
                'maintainability': max(80, min(100, new_performance + random.uniform(-5, 3)))
            }
        
        return None
    
    async def _detect_anomalies(self, twin: DigitalTwin):
        """Detect anomalies in digital twin data"""
        try:
            current_state = twin.current_state
            anomalies = []
            
            if twin.config.twin_type == TwinType.MACHINE:
                # Check temperature anomaly
                if current_state.get('temperature', 0) > 85:
                    anomalies.append({
                        'type': 'high_temperature',
                        'severity': 'warning',
                        'value': current_state['temperature'],
                        'threshold': 85,
                        'timestamp': time.time()
                    })
                
                # Check vibration anomaly
                if current_state.get('vibration', 0) > 2.0:
                    anomalies.append({
                        'type': 'high_vibration',
                        'severity': 'critical',
                        'value': current_state['vibration'],
                        'threshold': 2.0,
                        'timestamp': time.time()
                    })
            
            elif twin.config.twin_type == TwinType.PROCESS:
                # Check quality anomaly
                if current_state.get('quality_rate', 100) < 90:
                    anomalies.append({
                        'type': 'low_quality',
                        'severity': 'warning',
                        'value': current_state['quality_rate'],
                        'threshold': 90,
                        'timestamp': time.time()
                    })
            
            # Add new anomalies
            twin.anomalies.extend(anomalies)
            
            # Keep only recent anomalies (last 100)
            twin.anomalies = twin.anomalies[-100:]
            
        except Exception as e:
            logger.error(f"Anomaly detection failed for twin {twin.twin_id}: {e}")
    
    async def _generate_predictions(self, twin: DigitalTwin):
        """Generate predictions for digital twin"""
        try:
            # Simple prediction logic (can be enhanced with ML models)
            current_state = twin.current_state
            predictions = {}
            
            if twin.config.twin_type == TwinType.MACHINE:
                # Predict maintenance needs
                efficiency = current_state.get('efficiency', 100)
                runtime = current_state.get('runtime_hours', 0)
                
                if efficiency < 80 or runtime > 1000:
                    predictions['maintenance_needed'] = {
                        'probability': min(100, (1000 - efficiency) / 10 + runtime / 100),
                        'estimated_time': max(1, 100 - efficiency) * 24,  # hours
                        'type': 'preventive'
                    }
            
            twin.predictions = predictions
            
        except Exception as e:
            logger.error(f"Prediction generation failed for twin {twin.twin_id}: {e}")
    
    async def _store_historical_data(self, twin: DigitalTwin):
        """Store historical data for digital twin"""
        try:
            # Store current state as historical data point
            data_point = {
                'timestamp': time.time(),
                'state': twin.current_state.copy()
            }
            
            twin.historical_data.append(data_point)
            
            # Keep only recent data (last 1000 points)
            twin.historical_data = twin.historical_data[-1000:]
            
        except Exception as e:
            logger.error(f"Historical data storage failed for twin {twin.twin_id}: {e}")
    
    async def get_twin(self, twin_id: str) -> Optional[Dict[str, Any]]:
        """Get digital twin details"""
        if twin_id not in self.twins:
            return None
        
        twin = self.twins[twin_id]
        return {
            'twin_id': twin_id,
            'name': twin.config.name,
            'type': twin.config.twin_type.value,
            'status': twin.status.value,
            'created_at': twin.created_at,
            'last_updated': twin.last_updated,
            'current_state': twin.current_state,
            'predictions': twin.predictions,
            'recent_anomalies': twin.anomalies[-10:],  # Last 10 anomalies
            'config': asdict(twin.config)
        }
    
    async def list_twins(self) -> List[Dict[str, Any]]:
        """List all digital twins"""
        twins = []
        for twin_id in self.twins:
            twin_data = await self.get_twin(twin_id)
            if twin_data:
                twins.append(twin_data)
        return twins
    
    async def update_twin_config(self, twin_id: str, config_updates: Dict[str, Any]):
        """Update digital twin configuration"""
        if twin_id not in self.twins:
            raise DigitalTwinError(f"Twin {twin_id} not found")
        
        twin = self.twins[twin_id]
        
        # Update configuration
        for key, value in config_updates.items():
            if hasattr(twin.config, key):
                setattr(twin.config, key, value)
        
        # Restart updates if frequency changed
        if 'update_frequency' in config_updates:
            await self._stop_twin_updates(twin_id)
            await self._start_twin_updates(twin_id)
        
        logger.info(f"Updated configuration for twin {twin_id}")
    
    async def delete_twin(self, twin_id: str):
        """Delete a digital twin"""
        if twin_id not in self.twins:
            raise DigitalTwinError(f"Twin {twin_id} not found")
        
        # Stop updates
        await self._stop_twin_updates(twin_id)
        
        # Remove twin
        del self.twins[twin_id]
        
        logger.info(f"Deleted twin {twin_id}")

# Import random for simulation
import random
