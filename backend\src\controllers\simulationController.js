/**
 * Simulation Controller
 * Handles simulation-related API requests
 */

const simulationService = require('../services/simulationService');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errors');

class SimulationController {
  /**
   * Get all simulations
   */
  async getAllSimulations(req, res, next) {
    try {
      const { status, processType, limit = 20, offset = 0 } = req.query;
      
      const filters = {};
      if (status) filters.status = status;
      if (processType) filters.processType = processType;
      
      const result = await simulationService.getAllSimulations(filters, limit, offset);
      
      res.status(200).json({
        success: true,
        data: {
          simulations: result.simulations,
          total: result.total,
          limit: parseInt(limit),
          offset: parseInt(offset)
        }
      });
    } catch (error) {
      logger.error('Error getting simulations:', error);
      next(error);
    }
  }

  /**
   * Create a new simulation
   */
  async createSimulation(req, res, next) {
    try {
      const simulationData = {
        name: req.body.name,
        processType: req.body.processType,
        parameters: req.body.parameters,
        duration: req.body.duration,
        stepSize: req.body.stepSize || 1.0,
        autoOptimize: req.body.autoOptimize || false,
        realTime: req.body.realTime || false,
        userId: req.user.id
      };

      const simulation = await simulationService.createSimulation(simulationData);
      
      logger.info(`Simulation created: ${simulation.id} by user ${req.user.id}`);
      
      res.status(201).json({
        success: true,
        data: simulation,
        message: 'Simulation created successfully'
      });
    } catch (error) {
      logger.error('Error creating simulation:', error);
      next(error);
    }
  }

  /**
   * Get simulation by ID
   */
  async getSimulationById(req, res, next) {
    try {
      const { id } = req.params;
      
      const simulation = await simulationService.getSimulationById(id);
      
      if (!simulation) {
        throw new AppError('Simulation not found', 404);
      }
      
      res.status(200).json({
        success: true,
        data: simulation
      });
    } catch (error) {
      logger.error(`Error getting simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Update simulation
   */
  async updateSimulation(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      const simulation = await simulationService.updateSimulation(id, updateData);
      
      if (!simulation) {
        throw new AppError('Simulation not found', 404);
      }
      
      logger.info(`Simulation updated: ${id} by user ${req.user.id}`);
      
      res.status(200).json({
        success: true,
        data: simulation,
        message: 'Simulation updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Delete simulation
   */
  async deleteSimulation(req, res, next) {
    try {
      const { id } = req.params;
      
      const deleted = await simulationService.deleteSimulation(id);
      
      if (!deleted) {
        throw new AppError('Simulation not found', 404);
      }
      
      logger.info(`Simulation deleted: ${id} by user ${req.user.id}`);
      
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Start simulation
   */
  async startSimulation(req, res, next) {
    try {
      const { id } = req.params;
      
      const result = await simulationService.startSimulation(id);
      
      if (!result.success) {
        throw new AppError(result.message, 400);
      }
      
      logger.info(`Simulation started: ${id} by user ${req.user.id}`);
      
      res.status(200).json({
        success: true,
        data: result.simulation,
        message: 'Simulation started successfully'
      });
    } catch (error) {
      logger.error(`Error starting simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Stop simulation
   */
  async stopSimulation(req, res, next) {
    try {
      const { id } = req.params;
      
      const result = await simulationService.stopSimulation(id);
      
      if (!result.success) {
        throw new AppError(result.message, 400);
      }
      
      logger.info(`Simulation stopped: ${id} by user ${req.user.id}`);
      
      res.status(200).json({
        success: true,
        data: result.simulation,
        message: 'Simulation stopped successfully'
      });
    } catch (error) {
      logger.error(`Error stopping simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Pause simulation
   */
  async pauseSimulation(req, res, next) {
    try {
      const { id } = req.params;
      
      const result = await simulationService.pauseSimulation(id);
      
      if (!result.success) {
        throw new AppError(result.message, 400);
      }
      
      logger.info(`Simulation paused: ${id} by user ${req.user.id}`);
      
      res.status(200).json({
        success: true,
        data: result.simulation,
        message: 'Simulation paused successfully'
      });
    } catch (error) {
      logger.error(`Error pausing simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Resume simulation
   */
  async resumeSimulation(req, res, next) {
    try {
      const { id } = req.params;
      
      const result = await simulationService.resumeSimulation(id);
      
      if (!result.success) {
        throw new AppError(result.message, 400);
      }
      
      logger.info(`Simulation resumed: ${id} by user ${req.user.id}`);
      
      res.status(200).json({
        success: true,
        data: result.simulation,
        message: 'Simulation resumed successfully'
      });
    } catch (error) {
      logger.error(`Error resuming simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Get simulation metrics
   */
  async getSimulationMetrics(req, res, next) {
    try {
      const { id } = req.params;
      const { timeRange = '1h' } = req.query;
      
      const metrics = await simulationService.getSimulationMetrics(id, timeRange);
      
      if (!metrics) {
        throw new AppError('Simulation not found', 404);
      }
      
      res.status(200).json({
        success: true,
        data: {
          simulationId: id,
          timeRange,
          metrics: metrics.current,
          history: metrics.history,
          summary: metrics.summary
        }
      });
    } catch (error) {
      logger.error(`Error getting metrics for simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Get simulation logs
   */
  async getSimulationLogs(req, res, next) {
    try {
      const { id } = req.params;
      const { level, limit = 100, offset = 0 } = req.query;
      
      const logs = await simulationService.getSimulationLogs(id, { level, limit, offset });
      
      if (!logs) {
        throw new AppError('Simulation not found', 404);
      }
      
      res.status(200).json({
        success: true,
        data: {
          simulationId: id,
          logs: logs.entries,
          total: logs.total,
          limit: parseInt(limit),
          offset: parseInt(offset)
        }
      });
    } catch (error) {
      logger.error(`Error getting logs for simulation ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Export simulation data
   */
  async exportSimulationData(req, res, next) {
    try {
      const { id } = req.params;
      const { format = 'json', includeMetrics = true, includeLogs = false } = req.query;
      
      const exportData = await simulationService.exportSimulationData(id, {
        format,
        includeMetrics: includeMetrics === 'true',
        includeLogs: includeLogs === 'true'
      });
      
      if (!exportData) {
        throw new AppError('Simulation not found', 404);
      }
      
      // Set appropriate headers for file download
      const filename = `simulation_${id}_${new Date().toISOString().split('T')[0]}.${format}`;
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.status(200).json(exportData);
      } else if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.status(200).send(exportData);
      } else {
        throw new AppError('Unsupported export format', 400);
      }
      
      logger.info(`Simulation data exported: ${id} by user ${req.user.id}`);
    } catch (error) {
      logger.error(`Error exporting simulation data ${req.params.id}:`, error);
      next(error);
    }
  }

  /**
   * Clone simulation
   */
  async cloneSimulation(req, res, next) {
    try {
      const { id } = req.params;
      const { name } = req.body;
      
      const clonedSimulation = await simulationService.cloneSimulation(id, {
        name: name || `Clone of ${id}`,
        userId: req.user.id
      });
      
      if (!clonedSimulation) {
        throw new AppError('Simulation not found', 404);
      }
      
      logger.info(`Simulation cloned: ${id} -> ${clonedSimulation.id} by user ${req.user.id}`);
      
      res.status(201).json({
        success: true,
        data: clonedSimulation,
        message: 'Simulation cloned successfully'
      });
    } catch (error) {
      logger.error(`Error cloning simulation ${req.params.id}:`, error);
      next(error);
    }
  }
}

module.exports = new SimulationController();
