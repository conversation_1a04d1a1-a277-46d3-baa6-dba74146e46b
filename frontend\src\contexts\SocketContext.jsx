/**
 * Socket Context
 * Provides WebSocket connection and real-time communication throughout the application
 */

import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { toast } from 'react-hot-toast';

// Create context
const SocketContext = createContext();

// Socket provider component
export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const { isAuthenticated, token, user } = useAuth();
  const socketRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 1000; // Start with 1 second

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token && !socketRef.current) {
      initializeSocket();
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
        setSocket(null);
        setIsConnected(false);
      }
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [isAuthenticated, token]);

  const initializeSocket = () => {
    try {
      const socketUrl = process.env.VITE_WS_URL || 'ws://localhost:5000';
      
      const newSocket = io(socketUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: reconnectDelay,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: maxReconnectAttempts
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id);
        setIsConnected(true);
        setConnectionError(null);
        setReconnectAttempts(0);
        
        // Join user-specific room
        if (user?.id) {
          newSocket.emit('join-user-room', user.id);
        }
        
        toast.success('Connected to real-time updates', {
          duration: 2000,
          position: 'bottom-right'
        });
      });

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server disconnected the socket, manual reconnection needed
          toast.error('Connection lost. Please refresh the page.', {
            duration: 5000
          });
        } else {
          toast.warning('Connection lost. Attempting to reconnect...', {
            duration: 3000
          });
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setConnectionError(error.message);
        setReconnectAttempts(prev => prev + 1);
        
        if (reconnectAttempts >= maxReconnectAttempts) {
          toast.error('Failed to connect to real-time updates. Please refresh the page.', {
            duration: 5000
          });
        }
      });

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('Socket reconnected after', attemptNumber, 'attempts');
        setIsConnected(true);
        setConnectionError(null);
        setReconnectAttempts(0);
        
        toast.success('Reconnected to real-time updates', {
          duration: 2000,
          position: 'bottom-right'
        });
      });

      newSocket.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error);
        setConnectionError(error.message);
      });

      newSocket.on('reconnect_failed', () => {
        console.error('Socket reconnection failed');
        toast.error('Failed to reconnect. Please refresh the page.', {
          duration: 5000
        });
      });

      // Authentication error
      newSocket.on('auth_error', (error) => {
        console.error('Socket authentication error:', error);
        toast.error('Authentication failed. Please log in again.', {
          duration: 5000
        });
        // Could trigger logout here
      });

      // Server messages
      newSocket.on('server_message', (data) => {
        console.log('Server message:', data);
        
        if (data.type === 'notification') {
          toast(data.message, {
            icon: data.icon || '📢',
            duration: data.duration || 4000
          });
        }
      });

      socketRef.current = newSocket;
      setSocket(newSocket);
      
    } catch (error) {
      console.error('Failed to initialize socket:', error);
      setConnectionError(error.message);
      toast.error('Failed to initialize real-time connection', {
        duration: 5000
      });
    }
  };

  // Emit event with error handling
  const emit = (event, data, callback) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data, callback);
    } else {
      console.warn('Socket not connected. Cannot emit event:', event);
      if (callback) {
        callback({ error: 'Socket not connected' });
      }
    }
  };

  // Subscribe to event
  const on = (event, handler) => {
    if (socketRef.current) {
      socketRef.current.on(event, handler);
    }
  };

  // Unsubscribe from event
  const off = (event, handler) => {
    if (socketRef.current) {
      if (handler) {
        socketRef.current.off(event, handler);
      } else {
        socketRef.current.off(event);
      }
    }
  };

  // Subscribe to event once
  const once = (event, handler) => {
    if (socketRef.current) {
      socketRef.current.once(event, handler);
    }
  };

  // Join a room
  const joinRoom = (roomId) => {
    emit('join-room', { roomId });
  };

  // Leave a room
  const leaveRoom = (roomId) => {
    emit('leave-room', { roomId });
  };

  // Subscribe to simulation updates
  const subscribeToSimulation = (simulationId) => {
    emit('subscribe-simulation', { simulationId });
  };

  // Unsubscribe from simulation updates
  const unsubscribeFromSimulation = (simulationId) => {
    emit('unsubscribe-simulation', { simulationId });
  };

  // Subscribe to digital twin updates
  const subscribeToDigitalTwin = (twinId) => {
    emit('subscribe-digital-twin', { twinId });
  };

  // Unsubscribe from digital twin updates
  const unsubscribeFromDigitalTwin = (twinId) => {
    emit('unsubscribe-digital-twin', { twinId });
  };

  // Manual reconnect
  const reconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocket(null);
      setIsConnected(false);
    }
    
    if (isAuthenticated && token) {
      setTimeout(() => {
        initializeSocket();
      }, 1000);
    }
  };

  // Get connection status
  const getConnectionStatus = () => {
    return {
      isConnected,
      connectionError,
      reconnectAttempts,
      socketId: socketRef.current?.id
    };
  };

  // Context value
  const value = {
    // Socket instance
    socket: socketRef.current,
    
    // Connection state
    isConnected,
    connectionError,
    reconnectAttempts,
    
    // Socket methods
    emit,
    on,
    off,
    once,
    
    // Room methods
    joinRoom,
    leaveRoom,
    
    // Subscription methods
    subscribeToSimulation,
    unsubscribeFromSimulation,
    subscribeToDigitalTwin,
    unsubscribeFromDigitalTwin,
    
    // Utility methods
    reconnect,
    getConnectionStatus
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = () => {
  const context = useContext(SocketContext);
  
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  
  return context.socket;
};

// Custom hook to use socket methods
export const useSocketMethods = () => {
  const context = useContext(SocketContext);
  
  if (!context) {
    throw new Error('useSocketMethods must be used within a SocketProvider');
  }
  
  return {
    emit: context.emit,
    on: context.on,
    off: context.off,
    once: context.once,
    joinRoom: context.joinRoom,
    leaveRoom: context.leaveRoom,
    subscribeToSimulation: context.subscribeToSimulation,
    unsubscribeFromSimulation: context.unsubscribeFromSimulation,
    subscribeToDigitalTwin: context.subscribeToDigitalTwin,
    unsubscribeFromDigitalTwin: context.unsubscribeFromDigitalTwin,
    reconnect: context.reconnect,
    getConnectionStatus: context.getConnectionStatus,
    isConnected: context.isConnected
  };
};

export default SocketContext;
