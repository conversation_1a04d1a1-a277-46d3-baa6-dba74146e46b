"""
Simulation Engine
Core simulation execution engine for manufacturing processes
"""

import asyncio
import time
import logging
import random
import math
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .process_model import ProcessModel, ProcessState
from ..utils.exceptions import SimulationError

logger = logging.getLogger(__name__)

@dataclass
class SimulationStep:
    """Result of a single simulation step"""
    step_number: int
    timestamp: float
    metrics: Dict[str, Any]
    events: List[Dict[str, Any]]
    completed: bool = False

class SimulationEngine:
    """Core simulation engine for manufacturing processes"""
    
    def __init__(self, simulation_id: str, process_model: ProcessModel, 
                 step_size: float = 1.0, real_time: bool = False):
        self.simulation_id = simulation_id
        self.process_model = process_model
        self.step_size = step_size
        self.real_time = real_time
        
        # Simulation state
        self.current_time = 0.0
        self.step_number = 0
        self.is_initialized = False
        self.is_running = False
        
        # Performance tracking
        self.metrics_history: List[Dict[str, Any]] = []
        self.events_log: List[Dict[str, Any]] = []
        
        # Simulation parameters
        self.efficiency_factor = 1.0
        self.quality_factor = 1.0
        self.maintenance_schedule = {}
        
    async def initialize(self):
        """Initialize the simulation engine"""
        try:
            logger.info(f"Initializing simulation engine for {self.simulation_id}")
            
            # Initialize process model
            await self.process_model.initialize()
            
            # Reset simulation state
            self.current_time = 0.0
            self.step_number = 0
            self.metrics_history.clear()
            self.events_log.clear()
            
            # Initialize performance factors
            self.efficiency_factor = self._calculate_initial_efficiency()
            self.quality_factor = self._calculate_initial_quality()
            
            self.is_initialized = True
            self.is_running = True
            
            logger.info(f"Simulation engine initialized for {self.simulation_id}")
            
        except Exception as e:
            logger.error(f"Failed to initialize simulation engine: {e}")
            raise SimulationError(f"Engine initialization failed: {e}")
    
    async def step(self) -> SimulationStep:
        """Execute a single simulation step"""
        if not self.is_initialized:
            raise SimulationError("Simulation engine not initialized")
        
        try:
            step_start_time = time.time()
            
            # Advance simulation time
            self.current_time += self.step_size
            self.step_number += 1
            
            # Execute process model step
            process_result = await self.process_model.execute_step(
                self.current_time, self.step_size
            )
            
            # Calculate performance metrics
            metrics = await self._calculate_metrics(process_result)
            
            # Generate events
            events = await self._generate_events(process_result, metrics)
            
            # Update efficiency and quality factors
            self._update_performance_factors(metrics)
            
            # Check for maintenance requirements
            await self._check_maintenance_requirements(metrics)
            
            # Store metrics history
            self.metrics_history.append({
                'timestamp': self.current_time,
                'step': self.step_number,
                **metrics
            })
            
            # Store events
            self.events_log.extend(events)
            
            # Create step result
            step_result = SimulationStep(
                step_number=self.step_number,
                timestamp=self.current_time,
                metrics=metrics,
                events=events,
                completed=process_result.completed
            )
            
            # Log performance
            step_duration = time.time() - step_start_time
            if step_duration > 0.1:  # Log slow steps
                logger.warning(f"Slow simulation step {self.step_number}: {step_duration:.3f}s")
            
            return step_result
            
        except Exception as e:
            logger.error(f"Simulation step failed: {e}")
            raise SimulationError(f"Step execution failed: {e}")
    
    async def _calculate_metrics(self, process_result) -> Dict[str, Any]:
        """Calculate performance metrics for current step"""
        try:
            # Base metrics from process model
            base_metrics = process_result.metrics
            
            # Calculate throughput
            throughput = self._calculate_throughput(base_metrics)
            
            # Calculate efficiency
            efficiency = self._calculate_efficiency(base_metrics)
            
            # Calculate quality metrics
            quality_metrics = self._calculate_quality_metrics(base_metrics)
            
            # Calculate cost metrics
            cost_metrics = self._calculate_cost_metrics(base_metrics)
            
            # Calculate resource utilization
            resource_utilization = self._calculate_resource_utilization(base_metrics)
            
            # Calculate OEE (Overall Equipment Effectiveness)
            oee = self._calculate_oee(efficiency, quality_metrics['quality_rate'])
            
            # Combine all metrics
            metrics = {
                'throughput': throughput,
                'efficiency': efficiency,
                'oee': oee,
                'cycle_time': base_metrics.get('cycle_time', 0),
                'queue_length': base_metrics.get('queue_length', 0),
                'active_machines': base_metrics.get('active_machines', 0),
                'total_machines': base_metrics.get('total_machines', 0),
                **quality_metrics,
                **cost_metrics,
                **resource_utilization
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to calculate metrics: {e}")
            return {}
    
    def _calculate_throughput(self, base_metrics: Dict[str, Any]) -> float:
        """Calculate production throughput"""
        units_produced = base_metrics.get('units_produced', 0)
        time_elapsed = max(self.current_time, 1.0)  # Avoid division by zero
        return (units_produced / time_elapsed) * 3600  # Units per hour
    
    def _calculate_efficiency(self, base_metrics: Dict[str, Any]) -> float:
        """Calculate process efficiency"""
        actual_output = base_metrics.get('actual_output', 0)
        theoretical_output = base_metrics.get('theoretical_output', 1)
        
        if theoretical_output == 0:
            return 0.0
        
        base_efficiency = (actual_output / theoretical_output) * 100
        
        # Apply efficiency factor with some randomness
        efficiency_variance = random.uniform(-0.05, 0.05)  # ±5% variance
        return max(0, min(100, base_efficiency * self.efficiency_factor + efficiency_variance))
    
    def _calculate_quality_metrics(self, base_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate quality-related metrics"""
        total_units = base_metrics.get('total_units', 0)
        defective_units = base_metrics.get('defective_units', 0)
        
        if total_units == 0:
            quality_rate = 100.0
            defect_rate = 0.0
        else:
            quality_rate = ((total_units - defective_units) / total_units) * 100
            defect_rate = (defective_units / total_units) * 100
        
        # Apply quality factor with some randomness
        quality_variance = random.uniform(-0.02, 0.02)  # ±2% variance
        quality_rate = max(0, min(100, quality_rate * self.quality_factor + quality_variance))
        defect_rate = max(0, min(100, 100 - quality_rate))
        
        return {
            'quality_rate': quality_rate,
            'defect_rate': defect_rate,
            'total_units': total_units,
            'defective_units': defective_units,
            'good_units': total_units - defective_units
        }
    
    def _calculate_cost_metrics(self, base_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate cost-related metrics"""
        # Base costs
        material_cost_per_unit = base_metrics.get('material_cost_per_unit', 10.0)
        labor_cost_per_hour = base_metrics.get('labor_cost_per_hour', 25.0)
        machine_cost_per_hour = base_metrics.get('machine_cost_per_hour', 50.0)
        
        # Calculate costs
        units_produced = base_metrics.get('units_produced', 0)
        active_machines = base_metrics.get('active_machines', 0)
        
        material_cost = units_produced * material_cost_per_unit
        labor_cost = (self.step_size / 3600) * labor_cost_per_hour
        machine_cost = (self.step_size / 3600) * active_machines * machine_cost_per_hour
        
        total_cost = material_cost + labor_cost + machine_cost
        cost_per_unit = total_cost / max(units_produced, 1)
        
        return {
            'material_cost': material_cost,
            'labor_cost': labor_cost,
            'machine_cost': machine_cost,
            'total_cost': total_cost,
            'cost_per_unit': cost_per_unit
        }
    
    def _calculate_resource_utilization(self, base_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate resource utilization metrics"""
        active_machines = base_metrics.get('active_machines', 0)
        total_machines = base_metrics.get('total_machines', 1)
        active_workers = base_metrics.get('active_workers', 0)
        total_workers = base_metrics.get('total_workers', 1)
        
        machine_utilization = (active_machines / max(total_machines, 1)) * 100
        worker_utilization = (active_workers / max(total_workers, 1)) * 100
        
        return {
            'machine_utilization': machine_utilization,
            'worker_utilization': worker_utilization,
            'active_machines': active_machines,
            'total_machines': total_machines,
            'active_workers': active_workers,
            'total_workers': total_workers
        }
    
    def _calculate_oee(self, efficiency: float, quality_rate: float) -> float:
        """Calculate Overall Equipment Effectiveness"""
        # Assume 100% availability for simplicity (can be enhanced)
        availability = 100.0
        
        # OEE = Availability × Performance × Quality
        oee = (availability / 100) * (efficiency / 100) * (quality_rate / 100) * 100
        return min(100, max(0, oee))
    
    async def _generate_events(self, process_result, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate simulation events based on current state"""
        events = []
        
        # Quality events
        if metrics.get('defect_rate', 0) > 5.0:
            events.append({
                'type': 'quality_alert',
                'severity': 'warning',
                'message': f"High defect rate detected: {metrics['defect_rate']:.1f}%",
                'timestamp': self.current_time
            })
        
        # Efficiency events
        if metrics.get('efficiency', 100) < 70.0:
            events.append({
                'type': 'efficiency_alert',
                'severity': 'warning',
                'message': f"Low efficiency detected: {metrics['efficiency']:.1f}%",
                'timestamp': self.current_time
            })
        
        # Machine utilization events
        if metrics.get('machine_utilization', 0) > 95.0:
            events.append({
                'type': 'utilization_alert',
                'severity': 'info',
                'message': f"High machine utilization: {metrics['machine_utilization']:.1f}%",
                'timestamp': self.current_time
            })
        
        # Random maintenance events
        if random.random() < 0.001:  # 0.1% chance per step
            events.append({
                'type': 'maintenance_required',
                'severity': 'warning',
                'message': "Scheduled maintenance required for Machine #1",
                'timestamp': self.current_time
            })
        
        return events
    
    def _update_performance_factors(self, metrics: Dict[str, Any]):
        """Update efficiency and quality factors based on current performance"""
        # Gradually adjust factors based on performance
        efficiency = metrics.get('efficiency', 100)
        quality_rate = metrics.get('quality_rate', 100)
        
        # Efficiency factor adjustment
        if efficiency > 90:
            self.efficiency_factor = min(1.1, self.efficiency_factor + 0.001)
        elif efficiency < 70:
            self.efficiency_factor = max(0.8, self.efficiency_factor - 0.001)
        
        # Quality factor adjustment
        if quality_rate > 95:
            self.quality_factor = min(1.05, self.quality_factor + 0.0005)
        elif quality_rate < 90:
            self.quality_factor = max(0.9, self.quality_factor - 0.0005)
    
    async def _check_maintenance_requirements(self, metrics: Dict[str, Any]):
        """Check if maintenance is required"""
        # Simple maintenance logic based on machine utilization
        machine_utilization = metrics.get('machine_utilization', 0)
        
        if machine_utilization > 90 and random.random() < 0.01:
            # Schedule maintenance
            maintenance_time = self.current_time + random.uniform(100, 500)
            self.maintenance_schedule[maintenance_time] = {
                'type': 'preventive',
                'duration': random.uniform(30, 120),
                'machine_id': 'machine_1'
            }
    
    def _calculate_initial_efficiency(self) -> float:
        """Calculate initial efficiency factor"""
        # Base efficiency with some randomness
        return random.uniform(0.9, 1.1)
    
    def _calculate_initial_quality(self) -> float:
        """Calculate initial quality factor"""
        # Base quality with some randomness
        return random.uniform(0.95, 1.05)
    
    async def get_current_metrics(self) -> Dict[str, Any]:
        """Get current simulation metrics"""
        if not self.metrics_history:
            return {}
        
        return self.metrics_history[-1]
    
    async def get_metrics_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get metrics history"""
        return self.metrics_history[-limit:]
    
    async def get_events_log(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get events log"""
        return self.events_log[-limit:]
