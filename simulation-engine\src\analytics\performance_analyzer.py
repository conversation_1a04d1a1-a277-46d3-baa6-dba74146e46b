"""
Performance Analyzer
Advanced analytics for manufacturing performance metrics
"""

import asyncio
import logging
import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: float
    simulation_id: str
    throughput: float
    efficiency: float
    quality_rate: float
    oee: float
    cost_per_unit: float
    machine_utilization: float
    defect_rate: float
    cycle_time: float

@dataclass
class TrendAnalysis:
    """Trend analysis results"""
    metric_name: str
    trend_direction: str  # 'increasing', 'decreasing', 'stable'
    trend_strength: float  # 0-1
    slope: float
    r_squared: float
    forecast_values: List[float]
    confidence_interval: Tuple[float, float]

class PerformanceAnalyzer:
    """Analyzes manufacturing performance metrics and provides insights"""
    
    def __init__(self, config):
        self.config = config
        self.metrics_cache: Dict[str, List[PerformanceMetrics]] = {}
        self.analysis_cache: Dict[str, Dict[str, Any]] = {}
        self.benchmark_data: Dict[str, float] = {}
        
        # Initialize benchmark values
        self._initialize_benchmarks()
    
    def _initialize_benchmarks(self):
        """Initialize industry benchmark values"""
        self.benchmark_data = {
            'efficiency': 85.0,
            'quality_rate': 95.0,
            'oee': 80.0,
            'machine_utilization': 75.0,
            'defect_rate': 2.0,
            'throughput_target': 100.0
        }
    
    async def analyze_simulation_performance(self, simulation_id: str, 
                                           metrics_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance for a specific simulation"""
        try:
            # Convert to PerformanceMetrics objects
            metrics = self._convert_to_metrics(simulation_id, metrics_data)
            
            if not metrics:
                return {"error": "No metrics data available"}
            
            # Store in cache
            self.metrics_cache[simulation_id] = metrics
            
            # Perform various analyses
            analysis_results = {
                'summary_statistics': await self._calculate_summary_statistics(metrics),
                'trend_analysis': await self._perform_trend_analysis(metrics),
                'benchmark_comparison': await self._compare_to_benchmarks(metrics),
                'anomaly_detection': await self._detect_anomalies(metrics),
                'correlation_analysis': await self._analyze_correlations(metrics),
                'performance_score': await self._calculate_performance_score(metrics),
                'recommendations': await self._generate_recommendations(metrics)
            }
            
            # Cache results
            self.analysis_cache[simulation_id] = analysis_results
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"Performance analysis failed for simulation {simulation_id}: {e}")
            return {"error": str(e)}
    
    def _convert_to_metrics(self, simulation_id: str, 
                           metrics_data: List[Dict[str, Any]]) -> List[PerformanceMetrics]:
        """Convert raw metrics data to PerformanceMetrics objects"""
        metrics = []
        
        for data in metrics_data:
            try:
                metric = PerformanceMetrics(
                    timestamp=data.get('timestamp', time.time()),
                    simulation_id=simulation_id,
                    throughput=data.get('throughput', 0.0),
                    efficiency=data.get('efficiency', 0.0),
                    quality_rate=data.get('quality_rate', 0.0),
                    oee=data.get('oee', 0.0),
                    cost_per_unit=data.get('cost_per_unit', 0.0),
                    machine_utilization=data.get('machine_utilization', 0.0),
                    defect_rate=data.get('defect_rate', 0.0),
                    cycle_time=data.get('cycle_time', 0.0)
                )
                metrics.append(metric)
            except Exception as e:
                logger.warning(f"Failed to convert metrics data: {e}")
                continue
        
        return metrics
    
    async def _calculate_summary_statistics(self, metrics: List[PerformanceMetrics]) -> Dict[str, Any]:
        """Calculate summary statistics for all metrics"""
        if not metrics:
            return {}
        
        # Extract metric values
        throughput_values = [m.throughput for m in metrics]
        efficiency_values = [m.efficiency for m in metrics]
        quality_values = [m.quality_rate for m in metrics]
        oee_values = [m.oee for m in metrics]
        utilization_values = [m.machine_utilization for m in metrics]
        cost_values = [m.cost_per_unit for m in metrics]
        defect_values = [m.defect_rate for m in metrics]
        cycle_time_values = [m.cycle_time for m in metrics]
        
        def calculate_stats(values: List[float]) -> Dict[str, float]:
            if not values:
                return {}
            return {
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std_dev': statistics.stdev(values) if len(values) > 1 else 0.0,
                'min': min(values),
                'max': max(values),
                'range': max(values) - min(values),
                'q1': np.percentile(values, 25),
                'q3': np.percentile(values, 75)
            }
        
        return {
            'throughput': calculate_stats(throughput_values),
            'efficiency': calculate_stats(efficiency_values),
            'quality_rate': calculate_stats(quality_values),
            'oee': calculate_stats(oee_values),
            'machine_utilization': calculate_stats(utilization_values),
            'cost_per_unit': calculate_stats(cost_values),
            'defect_rate': calculate_stats(defect_values),
            'cycle_time': calculate_stats(cycle_time_values),
            'total_data_points': len(metrics),
            'time_span_hours': (metrics[-1].timestamp - metrics[0].timestamp) / 3600 if len(metrics) > 1 else 0
        }
    
    async def _perform_trend_analysis(self, metrics: List[PerformanceMetrics]) -> Dict[str, TrendAnalysis]:
        """Perform trend analysis on key metrics"""
        if len(metrics) < 3:
            return {}
        
        # Create time series data
        timestamps = [m.timestamp for m in metrics]
        time_normalized = [(t - timestamps[0]) / 3600 for t in timestamps]  # Hours from start
        
        trends = {}
        
        # Analyze trends for key metrics
        metric_extractors = {
            'throughput': lambda m: m.throughput,
            'efficiency': lambda m: m.efficiency,
            'quality_rate': lambda m: m.quality_rate,
            'oee': lambda m: m.oee,
            'machine_utilization': lambda m: m.machine_utilization,
            'defect_rate': lambda m: m.defect_rate
        }
        
        for metric_name, extractor in metric_extractors.items():
            values = [extractor(m) for m in metrics]
            trend = await self._analyze_single_trend(metric_name, time_normalized, values)
            if trend:
                trends[metric_name] = trend
        
        return trends
    
    async def _analyze_single_trend(self, metric_name: str, time_values: List[float], 
                                   metric_values: List[float]) -> Optional[TrendAnalysis]:
        """Analyze trend for a single metric"""
        try:
            if len(time_values) < 3 or len(metric_values) < 3:
                return None
            
            # Linear regression
            x = np.array(time_values)
            y = np.array(metric_values)
            
            # Calculate slope and R-squared
            slope, intercept = np.polyfit(x, y, 1)
            y_pred = slope * x + intercept
            
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
            
            # Determine trend direction and strength
            if abs(slope) < 0.1:
                trend_direction = 'stable'
            elif slope > 0:
                trend_direction = 'increasing'
            else:
                trend_direction = 'decreasing'
            
            trend_strength = min(1.0, abs(slope) / (np.std(y) + 1e-6))
            
            # Generate forecast (next 5 time points)
            forecast_times = [time_values[-1] + i for i in range(1, 6)]
            forecast_values = [slope * t + intercept for t in forecast_times]
            
            # Calculate confidence interval (simplified)
            std_error = np.sqrt(ss_res / (len(y) - 2)) if len(y) > 2 else 0
            confidence_interval = (
                forecast_values[-1] - 1.96 * std_error,
                forecast_values[-1] + 1.96 * std_error
            )
            
            return TrendAnalysis(
                metric_name=metric_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                slope=slope,
                r_squared=r_squared,
                forecast_values=forecast_values,
                confidence_interval=confidence_interval
            )
            
        except Exception as e:
            logger.error(f"Trend analysis failed for {metric_name}: {e}")
            return None
    
    async def _compare_to_benchmarks(self, metrics: List[PerformanceMetrics]) -> Dict[str, Any]:
        """Compare performance metrics to industry benchmarks"""
        if not metrics:
            return {}
        
        # Calculate current averages
        current_metrics = {
            'efficiency': statistics.mean([m.efficiency for m in metrics]),
            'quality_rate': statistics.mean([m.quality_rate for m in metrics]),
            'oee': statistics.mean([m.oee for m in metrics]),
            'machine_utilization': statistics.mean([m.machine_utilization for m in metrics]),
            'defect_rate': statistics.mean([m.defect_rate for m in metrics])
        }
        
        comparisons = {}
        
        for metric, current_value in current_metrics.items():
            if metric in self.benchmark_data:
                benchmark = self.benchmark_data[metric]
                
                if metric == 'defect_rate':
                    # Lower is better for defect rate
                    performance_ratio = benchmark / max(current_value, 0.1)
                    status = 'above_benchmark' if current_value < benchmark else 'below_benchmark'
                else:
                    # Higher is better for other metrics
                    performance_ratio = current_value / max(benchmark, 0.1)
                    status = 'above_benchmark' if current_value > benchmark else 'below_benchmark'
                
                comparisons[metric] = {
                    'current_value': current_value,
                    'benchmark_value': benchmark,
                    'performance_ratio': performance_ratio,
                    'status': status,
                    'gap': abs(current_value - benchmark),
                    'improvement_potential': max(0, benchmark - current_value) if metric != 'defect_rate' else max(0, current_value - benchmark)
                }
        
        return comparisons
    
    async def _detect_anomalies(self, metrics: List[PerformanceMetrics]) -> Dict[str, List[Dict[str, Any]]]:
        """Detect anomalies in performance metrics"""
        if len(metrics) < 10:
            return {}
        
        anomalies = {}
        
        # Define metric extractors and thresholds
        metric_configs = {
            'efficiency': {
                'extractor': lambda m: m.efficiency,
                'lower_threshold': 70.0,
                'upper_threshold': 100.0
            },
            'quality_rate': {
                'extractor': lambda m: m.quality_rate,
                'lower_threshold': 85.0,
                'upper_threshold': 100.0
            },
            'defect_rate': {
                'extractor': lambda m: m.defect_rate,
                'lower_threshold': 0.0,
                'upper_threshold': 5.0
            },
            'machine_utilization': {
                'extractor': lambda m: m.machine_utilization,
                'lower_threshold': 50.0,
                'upper_threshold': 100.0
            }
        }
        
        for metric_name, config in metric_configs.items():
            values = [config['extractor'](m) for m in metrics]
            timestamps = [m.timestamp for m in metrics]
            
            metric_anomalies = []
            
            # Statistical anomaly detection (Z-score method)
            if len(values) > 5:
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                
                for i, (value, timestamp) in enumerate(zip(values, timestamps)):
                    z_score = abs(value - mean_val) / max(std_val, 0.1)
                    
                    # Check for statistical anomalies
                    if z_score > 2.5:  # 2.5 standard deviations
                        metric_anomalies.append({
                            'type': 'statistical_outlier',
                            'timestamp': timestamp,
                            'value': value,
                            'z_score': z_score,
                            'severity': 'high' if z_score > 3.0 else 'medium'
                        })
                    
                    # Check for threshold violations
                    if value < config['lower_threshold'] or value > config['upper_threshold']:
                        metric_anomalies.append({
                            'type': 'threshold_violation',
                            'timestamp': timestamp,
                            'value': value,
                            'threshold': config['lower_threshold'] if value < config['lower_threshold'] else config['upper_threshold'],
                            'severity': 'high'
                        })
            
            if metric_anomalies:
                anomalies[metric_name] = metric_anomalies
        
        return anomalies
    
    async def _analyze_correlations(self, metrics: List[PerformanceMetrics]) -> Dict[str, float]:
        """Analyze correlations between different metrics"""
        if len(metrics) < 5:
            return {}
        
        # Extract metric values
        data = {
            'throughput': [m.throughput for m in metrics],
            'efficiency': [m.efficiency for m in metrics],
            'quality_rate': [m.quality_rate for m in metrics],
            'machine_utilization': [m.machine_utilization for m in metrics],
            'defect_rate': [m.defect_rate for m in metrics],
            'cost_per_unit': [m.cost_per_unit for m in metrics]
        }
        
        # Calculate correlation matrix
        df = pd.DataFrame(data)
        correlation_matrix = df.corr()
        
        # Extract significant correlations
        correlations = {}
        for i, col1 in enumerate(correlation_matrix.columns):
            for j, col2 in enumerate(correlation_matrix.columns):
                if i < j:  # Avoid duplicates
                    corr_value = correlation_matrix.iloc[i, j]
                    if abs(corr_value) > 0.3:  # Only significant correlations
                        correlations[f"{col1}_vs_{col2}"] = corr_value
        
        return correlations
    
    async def _calculate_performance_score(self, metrics: List[PerformanceMetrics]) -> Dict[str, Any]:
        """Calculate overall performance score"""
        if not metrics:
            return {}
        
        # Calculate weighted average of key metrics
        weights = {
            'efficiency': 0.25,
            'quality_rate': 0.25,
            'oee': 0.20,
            'machine_utilization': 0.15,
            'throughput': 0.15
        }
        
        # Normalize metrics to 0-100 scale
        avg_efficiency = statistics.mean([m.efficiency for m in metrics])
        avg_quality = statistics.mean([m.quality_rate for m in metrics])
        avg_oee = statistics.mean([m.oee for m in metrics])
        avg_utilization = statistics.mean([m.machine_utilization for m in metrics])
        avg_throughput = statistics.mean([m.throughput for m in metrics])
        
        # Normalize throughput (assuming target of 100 units/hour)
        normalized_throughput = min(100, (avg_throughput / 100) * 100)
        
        # Calculate weighted score
        performance_score = (
            weights['efficiency'] * avg_efficiency +
            weights['quality_rate'] * avg_quality +
            weights['oee'] * avg_oee +
            weights['machine_utilization'] * avg_utilization +
            weights['throughput'] * normalized_throughput
        )
        
        # Determine performance grade
        if performance_score >= 90:
            grade = 'A'
        elif performance_score >= 80:
            grade = 'B'
        elif performance_score >= 70:
            grade = 'C'
        elif performance_score >= 60:
            grade = 'D'
        else:
            grade = 'F'
        
        return {
            'overall_score': performance_score,
            'grade': grade,
            'component_scores': {
                'efficiency': avg_efficiency,
                'quality_rate': avg_quality,
                'oee': avg_oee,
                'machine_utilization': avg_utilization,
                'throughput': normalized_throughput
            },
            'weights': weights
        }
    
    async def _generate_recommendations(self, metrics: List[PerformanceMetrics]) -> List[Dict[str, Any]]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if not metrics:
            return recommendations
        
        # Calculate averages
        avg_efficiency = statistics.mean([m.efficiency for m in metrics])
        avg_quality = statistics.mean([m.quality_rate for m in metrics])
        avg_utilization = statistics.mean([m.machine_utilization for m in metrics])
        avg_defect_rate = statistics.mean([m.defect_rate for m in metrics])
        
        # Generate recommendations based on performance
        if avg_efficiency < 80:
            recommendations.append({
                'category': 'efficiency',
                'priority': 'high',
                'title': 'Improve Process Efficiency',
                'description': f'Current efficiency ({avg_efficiency:.1f}%) is below target. Consider process optimization and equipment maintenance.',
                'potential_impact': 'high',
                'estimated_improvement': f'{85 - avg_efficiency:.1f}% efficiency gain possible'
            })
        
        if avg_quality < 95:
            recommendations.append({
                'category': 'quality',
                'priority': 'high',
                'title': 'Enhance Quality Control',
                'description': f'Quality rate ({avg_quality:.1f}%) needs improvement. Implement stricter quality checks and operator training.',
                'potential_impact': 'medium',
                'estimated_improvement': f'{95 - avg_quality:.1f}% quality improvement possible'
            })
        
        if avg_utilization < 75:
            recommendations.append({
                'category': 'utilization',
                'priority': 'medium',
                'title': 'Increase Machine Utilization',
                'description': f'Machine utilization ({avg_utilization:.1f}%) is low. Consider better scheduling and reduced downtime.',
                'potential_impact': 'medium',
                'estimated_improvement': f'{75 - avg_utilization:.1f}% utilization increase possible'
            })
        
        if avg_defect_rate > 3:
            recommendations.append({
                'category': 'quality',
                'priority': 'high',
                'title': 'Reduce Defect Rate',
                'description': f'Defect rate ({avg_defect_rate:.1f}%) is too high. Investigate root causes and implement corrective actions.',
                'potential_impact': 'high',
                'estimated_improvement': f'{avg_defect_rate - 2:.1f}% defect reduction possible'
            })
        
        return recommendations
    
    async def get_simulation_metrics(self, simulation_id: str) -> Dict[str, Any]:
        """Get cached metrics for a simulation"""
        if simulation_id in self.analysis_cache:
            return self.analysis_cache[simulation_id]
        return {}
    
    async def get_dashboard_data(self) -> Dict[str, Any]:
        """Get aggregated dashboard data"""
        try:
            # Aggregate data from all simulations
            all_metrics = []
            for metrics_list in self.metrics_cache.values():
                all_metrics.extend(metrics_list)
            
            if not all_metrics:
                return {
                    'total_simulations': 0,
                    'active_simulations': 0,
                    'average_performance': {},
                    'recent_alerts': []
                }
            
            # Calculate overall statistics
            dashboard_data = {
                'total_simulations': len(self.metrics_cache),
                'active_simulations': len([s for s in self.metrics_cache.keys()]),
                'total_data_points': len(all_metrics),
                'average_performance': await self._calculate_summary_statistics(all_metrics),
                'recent_alerts': await self._get_recent_alerts(),
                'performance_trends': await self._get_performance_trends(),
                'top_performers': await self._get_top_performers(),
                'improvement_opportunities': await self._get_improvement_opportunities()
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Failed to get dashboard data: {e}")
            return {"error": str(e)}
    
    async def _get_recent_alerts(self) -> List[Dict[str, Any]]:
        """Get recent performance alerts"""
        alerts = []
        
        for simulation_id, analysis in self.analysis_cache.items():
            anomalies = analysis.get('anomaly_detection', {})
            for metric_name, metric_anomalies in anomalies.items():
                for anomaly in metric_anomalies[-5:]:  # Last 5 anomalies
                    alerts.append({
                        'simulation_id': simulation_id,
                        'metric': metric_name,
                        'type': anomaly['type'],
                        'severity': anomaly['severity'],
                        'timestamp': anomaly['timestamp'],
                        'value': anomaly.get('value', 0)
                    })
        
        # Sort by timestamp (most recent first)
        alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        return alerts[:20]  # Return top 20 recent alerts
    
    async def _get_performance_trends(self) -> Dict[str, Any]:
        """Get overall performance trends"""
        trends = {}
        
        for simulation_id, analysis in self.analysis_cache.items():
            sim_trends = analysis.get('trend_analysis', {})
            for metric_name, trend in sim_trends.items():
                if metric_name not in trends:
                    trends[metric_name] = []
                trends[metric_name].append({
                    'simulation_id': simulation_id,
                    'direction': trend.trend_direction,
                    'strength': trend.trend_strength
                })
        
        return trends
    
    async def _get_top_performers(self) -> List[Dict[str, Any]]:
        """Get top performing simulations"""
        performers = []
        
        for simulation_id, analysis in self.analysis_cache.items():
            performance_score = analysis.get('performance_score', {})
            if performance_score:
                performers.append({
                    'simulation_id': simulation_id,
                    'score': performance_score.get('overall_score', 0),
                    'grade': performance_score.get('grade', 'N/A')
                })
        
        # Sort by score (highest first)
        performers.sort(key=lambda x: x['score'], reverse=True)
        return performers[:10]  # Top 10 performers
    
    async def _get_improvement_opportunities(self) -> List[Dict[str, Any]]:
        """Get improvement opportunities across all simulations"""
        opportunities = []
        
        for simulation_id, analysis in self.analysis_cache.items():
            recommendations = analysis.get('recommendations', [])
            for rec in recommendations:
                opportunities.append({
                    'simulation_id': simulation_id,
                    'category': rec['category'],
                    'priority': rec['priority'],
                    'title': rec['title'],
                    'potential_impact': rec['potential_impact']
                })
        
        # Sort by priority and impact
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        impact_order = {'high': 3, 'medium': 2, 'low': 1}
        
        opportunities.sort(
            key=lambda x: (priority_order.get(x['priority'], 0), impact_order.get(x['potential_impact'], 0)),
            reverse=True
        )
        
        return opportunities[:15]  # Top 15 opportunities
