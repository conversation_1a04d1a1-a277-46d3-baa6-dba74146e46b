/**
 * ManufactureX Frontend Application
 * Main React application component
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';
import { ErrorBoundary } from 'react-error-boundary';

// Context Providers
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { SocketProvider } from '@/contexts/SocketContext';

// Components
import Layout from '@/components/Layout/Layout';
import ErrorFallback from '@/components/ErrorFallback/ErrorFallback';
import LoadingSpinner from '@/components/LoadingSpinner/LoadingSpinner';

// Pages
import Dashboard from '@/pages/Dashboard/Dashboard';
import Simulations from '@/pages/Simulations/Simulations';
import SimulationDetail from '@/pages/Simulations/SimulationDetail';
import DigitalTwins from '@/pages/DigitalTwins/DigitalTwins';
import DigitalTwinDetail from '@/pages/DigitalTwins/DigitalTwinDetail';
import Analytics from '@/pages/Analytics/Analytics';
import Optimization from '@/pages/Optimization/Optimization';
import DataSources from '@/pages/DataSources/DataSources';
import Settings from '@/pages/Settings/Settings';
import Login from '@/pages/Auth/Login';
import Register from '@/pages/Auth/Register';
import NotFound from '@/pages/NotFound/NotFound';

// Hooks
import { useAuth } from '@/hooks/useAuth';

// Styles
import '@/styles/globals.css';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

// Public Route Component (redirect if authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

function App() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo);
        // Here you could send error to logging service
      }}
    >
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider>
            <Router>
              <AuthProvider>
                <SocketProvider>
                  <div className="App">
                    <Routes>
                      {/* Public Routes */}
                      <Route
                        path="/login"
                        element={
                          <PublicRoute>
                            <Login />
                          </PublicRoute>
                        }
                      />
                      <Route
                        path="/register"
                        element={
                          <PublicRoute>
                            <Register />
                          </PublicRoute>
                        }
                      />

                      {/* Protected Routes */}
                      <Route
                        path="/"
                        element={
                          <ProtectedRoute>
                            <Layout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Navigate to="/dashboard" replace />} />
                        <Route path="dashboard" element={<Dashboard />} />
                        
                        {/* Simulations */}
                        <Route path="simulations" element={<Simulations />} />
                        <Route path="simulations/:id" element={<SimulationDetail />} />
                        
                        {/* Digital Twins */}
                        <Route path="digital-twins" element={<DigitalTwins />} />
                        <Route path="digital-twins/:id" element={<DigitalTwinDetail />} />
                        
                        {/* Analytics */}
                        <Route path="analytics" element={<Analytics />} />
                        
                        {/* Optimization */}
                        <Route path="optimization" element={<Optimization />} />
                        
                        {/* Data Sources */}
                        <Route path="data-sources" element={<DataSources />} />
                        
                        {/* Settings */}
                        <Route path="settings" element={<Settings />} />
                      </Route>

                      {/* 404 Route */}
                      <Route path="*" element={<NotFound />} />
                    </Routes>

                    {/* Global Toast Notifications */}
                    <Toaster
                      position="top-right"
                      toastOptions={{
                        duration: 4000,
                        style: {
                          background: '#363636',
                          color: '#fff',
                        },
                        success: {
                          duration: 3000,
                          iconTheme: {
                            primary: '#10B981',
                            secondary: '#fff',
                          },
                        },
                        error: {
                          duration: 5000,
                          iconTheme: {
                            primary: '#EF4444',
                            secondary: '#fff',
                          },
                        },
                      }}
                    />
                  </div>
                </SocketProvider>
              </AuthProvider>
            </Router>
          </ThemeProvider>
          
          {/* React Query DevTools */}
          {process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
