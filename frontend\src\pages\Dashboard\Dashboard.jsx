/**
 * Dashboard Page
 * Main dashboard with overview of manufacturing operations
 */

import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  CogIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

// Components
import StatCard from '@/components/Dashboard/StatCard';
import PerformanceChart from '@/components/Dashboard/PerformanceChart';
import SimulationsList from '@/components/Dashboard/SimulationsList';
import AlertsPanel from '@/components/Dashboard/AlertsPanel';
import DigitalTwinsOverview from '@/components/Dashboard/DigitalTwinsOverview';
import RealtimeMetrics from '@/components/Dashboard/RealtimeMetrics';
import LoadingSpinner from '@/components/LoadingSpinner/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage/ErrorMessage';

// Services
import { dashboardService } from '@/services/dashboardService';
import { simulationService } from '@/services/simulationService';

// Hooks
import { useSocket } from '@/hooks/useSocket';
import { useRealTimeData } from '@/hooks/useRealTimeData';

const Dashboard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');
  const [realtimeData, setRealtimeData] = useState({});

  // Socket connection for real-time updates
  const socket = useSocket();

  // Fetch dashboard data
  const {
    data: dashboardData,
    isLoading: isDashboardLoading,
    error: dashboardError,
    refetch: refetchDashboard
  } = useQuery({
    queryKey: ['dashboard', selectedTimeRange],
    queryFn: () => dashboardService.getDashboardData(selectedTimeRange),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch active simulations
  const {
    data: simulationsData,
    isLoading: isSimulationsLoading,
    error: simulationsError
  } = useQuery({
    queryKey: ['simulations', 'active'],
    queryFn: () => simulationService.getSimulations({ status: 'running' }),
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  // Real-time data hook
  const { data: liveMetrics } = useRealTimeData('dashboard-metrics');

  // Socket event listeners
  useEffect(() => {
    if (!socket) return;

    const handleMetricsUpdate = (data) => {
      setRealtimeData(prev => ({
        ...prev,
        ...data
      }));
    };

    const handleSimulationUpdate = () => {
      refetchDashboard();
    };

    socket.on('metrics:update', handleMetricsUpdate);
    socket.on('simulation:update', handleSimulationUpdate);
    socket.on('alert:new', handleSimulationUpdate);

    return () => {
      socket.off('metrics:update', handleMetricsUpdate);
      socket.off('simulation:update', handleSimulationUpdate);
      socket.off('alert:new', handleSimulationUpdate);
    };
  }, [socket, refetchDashboard]);

  // Loading state
  if (isDashboardLoading || isSimulationsLoading) {
    return <LoadingSpinner />;
  }

  // Error state
  if (dashboardError || simulationsError) {
    return (
      <ErrorMessage
        title="Failed to load dashboard"
        message={dashboardError?.message || simulationsError?.message}
        onRetry={refetchDashboard}
      />
    );
  }

  const stats = dashboardData?.stats || {};
  const alerts = dashboardData?.alerts || [];
  const performanceData = dashboardData?.performance || [];
  const digitalTwins = dashboardData?.digitalTwins || [];
  const activeSimulations = simulationsData?.simulations || [];

  return (
    <>
      <Helmet>
        <title>Dashboard - ManufactureX</title>
        <meta name="description" content="Manufacturing simulation dashboard with real-time metrics and analytics" />
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manufacturing operations overview and real-time monitoring
            </p>
          </div>

          {/* Time Range Selector */}
          <div className="flex space-x-2">
            {['1h', '6h', '24h', '7d', '30d'].map((range) => (
              <button
                key={range}
                onClick={() => setSelectedTimeRange(range)}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  selectedTimeRange === range
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                {range}
              </button>
            ))}
          </div>
        </div>

        {/* Key Performance Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          <StatCard
            title="Active Simulations"
            value={stats.activeSimulations || 0}
            change={stats.simulationsChange || 0}
            icon={PlayIcon}
            color="blue"
          />
          <StatCard
            title="Overall Efficiency"
            value={`${(stats.overallEfficiency || 0).toFixed(1)}%`}
            change={stats.efficiencyChange || 0}
            icon={ChartBarIcon}
            color="green"
          />
          <StatCard
            title="Digital Twins"
            value={stats.digitalTwinsCount || 0}
            change={stats.digitalTwinsChange || 0}
            icon={CogIcon}
            color="purple"
          />
          <StatCard
            title="Active Alerts"
            value={stats.activeAlerts || 0}
            change={stats.alertsChange || 0}
            icon={ExclamationTriangleIcon}
            color="red"
          />
        </motion.div>

        {/* Real-time Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <RealtimeMetrics data={liveMetrics} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Performance Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Performance Overview
                </h2>
                <div className="flex space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Live
                  </span>
                </div>
              </div>
              <PerformanceChart data={performanceData} timeRange={selectedTimeRange} />
            </div>
          </motion.div>

          {/* Alerts Panel */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <AlertsPanel alerts={alerts} />
          </motion.div>
        </div>

        {/* Secondary Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Active Simulations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Active Simulations
                </h2>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {activeSimulations.length} running
                </span>
              </div>
              <SimulationsList simulations={activeSimulations} />
            </div>
          </motion.div>

          {/* Digital Twins Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Digital Twins
                </h2>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {digitalTwins.length} active
                </span>
              </div>
              <DigitalTwinsOverview twins={digitalTwins} />
            </div>
          </motion.div>
        </div>

        {/* System Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            System Status
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Simulation Engine: Online
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Data Collector: Active
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Analytics: Processing
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default Dashboard;
