# ManufactureX Grafana Datasources Configuration
# Automatic provisioning of data sources

apiVersion: 1

datasources:
  # Prometheus - Main metrics datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"
    secureJsonData: {}
    version: 1

  # InfluxDB - Time series data
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: metrics
    user: admin
    secureJsonData:
      password: influx123
    jsonData:
      timeInterval: "10s"
      httpMode: "GET"
      keepCookies: []
    editable: true
    version: 1

  # PostgreSQL - Application database
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: manufacturex
    user: postgres
    secureJsonData:
      password: password123
    jsonData:
      sslmode: "disable"
      maxOpenConns: 0
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1300
      timescaledb: false
    editable: true
    version: 1

  # Loki - Log aggregation (if available)
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: "prometheus"
          matcherRegex: "traceID=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"
    editable: true
    version: 1

  # TestData - For testing and development
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData:
      csvContent: |
        time,value
        2023-01-01 00:00:00,100
        2023-01-01 01:00:00,110
        2023-01-01 02:00:00,105
    version: 1

  # JSON API - For custom API endpoints
  - name: ManufactureX API
    type: marcusolsson-json-datasource
    access: proxy
    url: http://backend:5000/api
    jsonData:
      timeout: 30
      headers:
        - name: "Content-Type"
          value: "application/json"
        - name: "Accept"
          value: "application/json"
      queryParams:
        - name: "format"
          value: "json"
    secureJsonData:
      apiKey: ""
    editable: true
    version: 1
