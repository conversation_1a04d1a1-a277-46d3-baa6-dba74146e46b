"""
Configuration Management
Centralized configuration for the simulation engine
"""

import os
import logging
from typing import Any, Dict, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class Config:
    """Configuration manager for the simulation engine"""
    
    def __init__(self):
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from environment variables"""
        
        # Server Configuration
        self._config.update({
            'SIMULATION_HOST': os.getenv('SIMULATION_HOST', '0.0.0.0'),
            'SIMULATION_PORT': int(os.getenv('SIMULATION_PORT', 8000)),
            'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
            'WORKERS': int(os.getenv('WORKERS', 4)),
            'LOG_LEVEL': os.getenv('LOG_LEVEL', 'info').upper(),
        })
        
        # Database Configuration
        self._config.update({
            'DATABASE_URL': os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/manufacturex'),
            'DB_POOL_SIZE': int(os.getenv('DB_POOL_SIZE', 10)),
            'DB_MAX_OVERFLOW': int(os.getenv('DB_MAX_OVERFLOW', 20)),
            'DB_POOL_TIMEOUT': int(os.getenv('DB_POOL_TIMEOUT', 30)),
        })
        
        # Redis Configuration
        self._config.update({
            'REDIS_URL': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
            'REDIS_MAX_CONNECTIONS': int(os.getenv('REDIS_MAX_CONNECTIONS', 10)),
            'REDIS_RETRY_ON_TIMEOUT': os.getenv('REDIS_RETRY_ON_TIMEOUT', 'true').lower() == 'true',
        })
        
        # MQTT Configuration
        self._config.update({
            'MQTT_BROKER_URL': os.getenv('MQTT_BROKER_URL', 'mqtt://localhost:1883'),
            'MQTT_USERNAME': os.getenv('MQTT_USERNAME'),
            'MQTT_PASSWORD': os.getenv('MQTT_PASSWORD'),
            'MQTT_CLIENT_ID': os.getenv('MQTT_CLIENT_ID', 'manufacturex-simulation'),
            'MQTT_KEEPALIVE': int(os.getenv('MQTT_KEEPALIVE', 60)),
        })
        
        # InfluxDB Configuration
        self._config.update({
            'INFLUXDB_URL': os.getenv('INFLUXDB_URL', 'http://localhost:8086'),
            'INFLUXDB_TOKEN': os.getenv('INFLUXDB_TOKEN'),
            'INFLUXDB_ORG': os.getenv('INFLUXDB_ORG', 'manufacturex'),
            'INFLUXDB_BUCKET': os.getenv('INFLUXDB_BUCKET', 'metrics'),
        })
        
        # Simulation Configuration
        self._config.update({
            'MAX_CONCURRENT_SIMULATIONS': int(os.getenv('MAX_CONCURRENT_SIMULATIONS', 10)),
            'DEFAULT_STEP_SIZE': float(os.getenv('DEFAULT_STEP_SIZE', 1.0)),
            'MAX_SIMULATION_DURATION': int(os.getenv('MAX_SIMULATION_DURATION', 86400)),  # 24 hours
            'SIMULATION_TIMEOUT': int(os.getenv('SIMULATION_TIMEOUT', 3600)),  # 1 hour
            'AUTO_CLEANUP_INTERVAL': int(os.getenv('AUTO_CLEANUP_INTERVAL', 300)),  # 5 minutes
        })
        
        # Performance Configuration
        self._config.update({
            'METRICS_COLLECTION_INTERVAL': float(os.getenv('METRICS_COLLECTION_INTERVAL', 1.0)),
            'METRICS_RETENTION_HOURS': int(os.getenv('METRICS_RETENTION_HOURS', 168)),  # 7 days
            'MAX_MEMORY_USAGE_MB': int(os.getenv('MAX_MEMORY_USAGE_MB', 2048)),
            'MAX_CPU_USAGE_PERCENT': int(os.getenv('MAX_CPU_USAGE_PERCENT', 80)),
        })
        
        # Optimization Configuration
        self._config.update({
            'OPTIMIZATION_ENABLED': os.getenv('OPTIMIZATION_ENABLED', 'true').lower() == 'true',
            'OPTIMIZATION_ALGORITHMS': os.getenv('OPTIMIZATION_ALGORITHMS', 'genetic_algorithm,simulated_annealing').split(','),
            'MAX_OPTIMIZATION_ITERATIONS': int(os.getenv('MAX_OPTIMIZATION_ITERATIONS', 1000)),
            'OPTIMIZATION_TIMEOUT': int(os.getenv('OPTIMIZATION_TIMEOUT', 300)),  # 5 minutes
        })
        
        # Machine Learning Configuration
        self._config.update({
            'ML_ENABLED': os.getenv('ML_ENABLED', 'true').lower() == 'true',
            'ML_MODEL_PATH': os.getenv('ML_MODEL_PATH', './models'),
            'ML_TRAINING_ENABLED': os.getenv('ML_TRAINING_ENABLED', 'false').lower() == 'true',
            'ML_PREDICTION_INTERVAL': int(os.getenv('ML_PREDICTION_INTERVAL', 60)),  # 1 minute
        })
        
        # Security Configuration
        self._config.update({
            'API_KEY_REQUIRED': os.getenv('API_KEY_REQUIRED', 'false').lower() == 'true',
            'API_KEY': os.getenv('API_KEY'),
            'RATE_LIMIT_ENABLED': os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true',
            'RATE_LIMIT_REQUESTS': int(os.getenv('RATE_LIMIT_REQUESTS', 100)),
            'RATE_LIMIT_WINDOW': int(os.getenv('RATE_LIMIT_WINDOW', 60)),  # 1 minute
        })
        
        # File Storage Configuration
        self._config.update({
            'DATA_DIR': Path(os.getenv('DATA_DIR', './data')),
            'LOGS_DIR': Path(os.getenv('LOGS_DIR', './logs')),
            'TEMP_DIR': Path(os.getenv('TEMP_DIR', './temp')),
            'MODELS_DIR': Path(os.getenv('MODELS_DIR', './models')),
            'MAX_FILE_SIZE_MB': int(os.getenv('MAX_FILE_SIZE_MB', 100)),
        })
        
        # External Services Configuration
        self._config.update({
            'WEATHER_API_KEY': os.getenv('WEATHER_API_KEY'),
            'WEATHER_API_URL': os.getenv('WEATHER_API_URL', 'https://api.openweathermap.org/data/2.5'),
            'EXTERNAL_API_TIMEOUT': int(os.getenv('EXTERNAL_API_TIMEOUT', 30)),
            'EXTERNAL_API_RETRIES': int(os.getenv('EXTERNAL_API_RETRIES', 3)),
        })
        
        # Monitoring Configuration
        self._config.update({
            'MONITORING_ENABLED': os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',
            'PROMETHEUS_ENABLED': os.getenv('PROMETHEUS_ENABLED', 'true').lower() == 'true',
            'PROMETHEUS_PORT': int(os.getenv('PROMETHEUS_PORT', 9090)),
            'HEALTH_CHECK_INTERVAL': int(os.getenv('HEALTH_CHECK_INTERVAL', 30)),
        })
        
        # Feature Flags
        self._config.update({
            'FEATURE_DIGITAL_TWINS': os.getenv('FEATURE_DIGITAL_TWINS', 'true').lower() == 'true',
            'FEATURE_REAL_TIME_OPTIMIZATION': os.getenv('FEATURE_REAL_TIME_OPTIMIZATION', 'true').lower() == 'true',
            'FEATURE_PREDICTIVE_ANALYTICS': os.getenv('FEATURE_PREDICTIVE_ANALYTICS', 'true').lower() == 'true',
            'FEATURE_ADVANCED_VISUALIZATION': os.getenv('FEATURE_ADVANCED_VISUALIZATION', 'true').lower() == 'true',
        })
        
        # Create directories if they don't exist
        self._create_directories()
        
        logger.info("Configuration loaded successfully")
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self._config['DATA_DIR'],
            self._config['LOGS_DIR'],
            self._config['TEMP_DIR'],
            self._config['MODELS_DIR']
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
                logger.debug(f"Created directory: {directory}")
            except Exception as e:
                logger.error(f"Failed to create directory {directory}: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value"""
        self._config[key] = value
        logger.debug(f"Configuration updated: {key} = {value}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            'url': self._config['DATABASE_URL'],
            'pool_size': self._config['DB_POOL_SIZE'],
            'max_overflow': self._config['DB_MAX_OVERFLOW'],
            'pool_timeout': self._config['DB_POOL_TIMEOUT']
        }
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration"""
        return {
            'url': self._config['REDIS_URL'],
            'max_connections': self._config['REDIS_MAX_CONNECTIONS'],
            'retry_on_timeout': self._config['REDIS_RETRY_ON_TIMEOUT']
        }
    
    def get_mqtt_config(self) -> Dict[str, Any]:
        """Get MQTT configuration"""
        return {
            'broker_url': self._config['MQTT_BROKER_URL'],
            'username': self._config['MQTT_USERNAME'],
            'password': self._config['MQTT_PASSWORD'],
            'client_id': self._config['MQTT_CLIENT_ID'],
            'keepalive': self._config['MQTT_KEEPALIVE']
        }
    
    def get_influxdb_config(self) -> Dict[str, Any]:
        """Get InfluxDB configuration"""
        return {
            'url': self._config['INFLUXDB_URL'],
            'token': self._config['INFLUXDB_TOKEN'],
            'org': self._config['INFLUXDB_ORG'],
            'bucket': self._config['INFLUXDB_BUCKET']
        }
    
    def get_simulation_config(self) -> Dict[str, Any]:
        """Get simulation configuration"""
        return {
            'max_concurrent': self._config['MAX_CONCURRENT_SIMULATIONS'],
            'default_step_size': self._config['DEFAULT_STEP_SIZE'],
            'max_duration': self._config['MAX_SIMULATION_DURATION'],
            'timeout': self._config['SIMULATION_TIMEOUT'],
            'cleanup_interval': self._config['AUTO_CLEANUP_INTERVAL']
        }
    
    def get_optimization_config(self) -> Dict[str, Any]:
        """Get optimization configuration"""
        return {
            'enabled': self._config['OPTIMIZATION_ENABLED'],
            'algorithms': self._config['OPTIMIZATION_ALGORITHMS'],
            'max_iterations': self._config['MAX_OPTIMIZATION_ITERATIONS'],
            'timeout': self._config['OPTIMIZATION_TIMEOUT']
        }
    
    def get_ml_config(self) -> Dict[str, Any]:
        """Get machine learning configuration"""
        return {
            'enabled': self._config['ML_ENABLED'],
            'model_path': self._config['ML_MODEL_PATH'],
            'training_enabled': self._config['ML_TRAINING_ENABLED'],
            'prediction_interval': self._config['ML_PREDICTION_INTERVAL']
        }
    
    def is_debug(self) -> bool:
        """Check if debug mode is enabled"""
        return self._config['DEBUG']
    
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled"""
        feature_key = f'FEATURE_{feature.upper()}'
        return self._config.get(feature_key, False)
    
    def validate(self) -> bool:
        """Validate configuration"""
        required_keys = [
            'SIMULATION_HOST',
            'SIMULATION_PORT',
            'DATABASE_URL',
            'REDIS_URL'
        ]
        
        missing_keys = []
        for key in required_keys:
            if not self._config.get(key):
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"Missing required configuration keys: {missing_keys}")
            return False
        
        # Validate port ranges
        if not (1 <= self._config['SIMULATION_PORT'] <= 65535):
            logger.error(f"Invalid simulation port: {self._config['SIMULATION_PORT']}")
            return False
        
        # Validate positive integers
        positive_int_keys = [
            'WORKERS',
            'MAX_CONCURRENT_SIMULATIONS',
            'MAX_SIMULATION_DURATION',
            'SIMULATION_TIMEOUT'
        ]
        
        for key in positive_int_keys:
            if self._config.get(key, 0) <= 0:
                logger.error(f"Invalid value for {key}: {self._config.get(key)}")
                return False
        
        logger.info("Configuration validation passed")
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        # Convert Path objects to strings for serialization
        config_dict = {}
        for key, value in self._config.items():
            if isinstance(value, Path):
                config_dict[key] = str(value)
            else:
                config_dict[key] = value
        return config_dict
    
    def __str__(self) -> str:
        """String representation of configuration"""
        return f"Config(debug={self.is_debug()}, host={self.get('SIMULATION_HOST')}, port={self.get('SIMULATION_PORT')})"
    
    def __repr__(self) -> str:
        """Detailed string representation"""
        return f"Config({self.to_dict()})"
