#!/usr/bin/env python3
"""
ManufactureX Simulation Engine
Main entry point for the manufacturing simulation system
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from core.simulation_manager import SimulationManager
from core.digital_twin_manager import DigitalTwinManager
from analytics.performance_analyzer import PerformanceAnalyzer
from optimization.process_optimizer import ProcessOptimizer
from data_processing.data_collector import DataCollector
from utils.config import Config
from utils.logger import setup_logger

# Global instances
simulation_manager = None
digital_twin_manager = None
performance_analyzer = None
process_optimizer = None
data_collector = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global simulation_manager, digital_twin_manager, performance_analyzer
    global process_optimizer, data_collector
    
    # Startup
    logger.info("Starting ManufactureX Simulation Engine...")
    
    # Initialize core components
    config = Config()
    simulation_manager = SimulationManager(config)
    digital_twin_manager = DigitalTwinManager(config)
    performance_analyzer = PerformanceAnalyzer(config)
    process_optimizer = ProcessOptimizer(config)
    data_collector = DataCollector(config)
    
    # Start background services
    await simulation_manager.initialize()
    await digital_twin_manager.initialize()
    await data_collector.start()
    
    logger.info("Simulation Engine started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Simulation Engine...")
    await data_collector.stop()
    await simulation_manager.shutdown()
    await digital_twin_manager.shutdown()
    logger.info("Simulation Engine stopped")

# Create FastAPI app
app = FastAPI(
    title="ManufactureX Simulation Engine",
    description="Advanced Manufacturing Simulation and Digital Twin Platform",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logger(__name__)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "ManufactureX Simulation Engine",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        health_status = {
            "status": "healthy",
            "components": {
                "simulation_manager": simulation_manager.is_healthy() if simulation_manager else False,
                "digital_twin_manager": digital_twin_manager.is_healthy() if digital_twin_manager else False,
                "data_collector": data_collector.is_running() if data_collector else False,
            },
            "timestamp": asyncio.get_event_loop().time()
        }
        return health_status
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.post("/simulations")
async def create_simulation(simulation_config: dict, background_tasks: BackgroundTasks):
    """Create a new simulation"""
    try:
        simulation_id = await simulation_manager.create_simulation(simulation_config)
        background_tasks.add_task(simulation_manager.start_simulation, simulation_id)
        
        return {
            "simulation_id": simulation_id,
            "status": "created",
            "message": "Simulation created and starting"
        }
    except Exception as e:
        logger.error(f"Failed to create simulation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/simulations")
async def list_simulations():
    """List all simulations"""
    try:
        simulations = await simulation_manager.list_simulations()
        return {"simulations": simulations}
    except Exception as e:
        logger.error(f"Failed to list simulations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/simulations/{simulation_id}")
async def get_simulation(simulation_id: str):
    """Get simulation details"""
    try:
        simulation = await simulation_manager.get_simulation(simulation_id)
        if not simulation:
            raise HTTPException(status_code=404, detail="Simulation not found")
        return simulation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get simulation {simulation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/simulations/{simulation_id}/start")
async def start_simulation(simulation_id: str, background_tasks: BackgroundTasks):
    """Start a simulation"""
    try:
        background_tasks.add_task(simulation_manager.start_simulation, simulation_id)
        return {"message": "Simulation starting", "simulation_id": simulation_id}
    except Exception as e:
        logger.error(f"Failed to start simulation {simulation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/simulations/{simulation_id}/stop")
async def stop_simulation(simulation_id: str):
    """Stop a simulation"""
    try:
        await simulation_manager.stop_simulation(simulation_id)
        return {"message": "Simulation stopped", "simulation_id": simulation_id}
    except Exception as e:
        logger.error(f"Failed to stop simulation {simulation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/simulations/{simulation_id}/metrics")
async def get_simulation_metrics(simulation_id: str):
    """Get simulation performance metrics"""
    try:
        metrics = await performance_analyzer.get_simulation_metrics(simulation_id)
        return {"simulation_id": simulation_id, "metrics": metrics}
    except Exception as e:
        logger.error(f"Failed to get metrics for simulation {simulation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/digital-twins")
async def create_digital_twin(twin_config: dict):
    """Create a new digital twin"""
    try:
        twin_id = await digital_twin_manager.create_twin(twin_config)
        return {
            "twin_id": twin_id,
            "status": "created",
            "message": "Digital twin created successfully"
        }
    except Exception as e:
        logger.error(f"Failed to create digital twin: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/digital-twins")
async def list_digital_twins():
    """List all digital twins"""
    try:
        twins = await digital_twin_manager.list_twins()
        return {"digital_twins": twins}
    except Exception as e:
        logger.error(f"Failed to list digital twins: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/digital-twins/{twin_id}")
async def get_digital_twin(twin_id: str):
    """Get digital twin details"""
    try:
        twin = await digital_twin_manager.get_twin(twin_id)
        if not twin:
            raise HTTPException(status_code=404, detail="Digital twin not found")
        return twin
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get digital twin {twin_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimization/{simulation_id}")
async def optimize_process(simulation_id: str, optimization_params: dict):
    """Run process optimization"""
    try:
        results = await process_optimizer.optimize(simulation_id, optimization_params)
        return {
            "simulation_id": simulation_id,
            "optimization_results": results,
            "status": "completed"
        }
    except Exception as e:
        logger.error(f"Failed to optimize process for simulation {simulation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/dashboard")
async def get_dashboard_data():
    """Get dashboard analytics data"""
    try:
        dashboard_data = await performance_analyzer.get_dashboard_data()
        return dashboard_data
    except Exception as e:
        logger.error(f"Failed to get dashboard data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

def main():
    """Main entry point"""
    config = Config()
    
    # Configure uvicorn
    uvicorn_config = {
        "host": config.get("SIMULATION_HOST", "0.0.0.0"),
        "port": config.get("SIMULATION_PORT", 8000),
        "log_level": config.get("LOG_LEVEL", "info").lower(),
        "reload": config.get("DEBUG", False),
        "workers": 1 if config.get("DEBUG", False) else config.get("WORKERS", 4)
    }
    
    logger.info(f"Starting server on {uvicorn_config['host']}:{uvicorn_config['port']}")
    
    # Run the server
    uvicorn.run("main:app", **uvicorn_config)

if __name__ == "__main__":
    main()
