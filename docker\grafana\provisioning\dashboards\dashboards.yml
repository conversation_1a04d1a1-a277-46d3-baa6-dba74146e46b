# ManufactureX Grafana Dashboards Configuration
# Automatic provisioning of dashboards

apiVersion: 1

providers:
  # Manufacturing Overview Dashboard
  - name: 'ManufactureX Manufacturing'
    orgId: 1
    folder: 'ManufactureX'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/manufacturing

  # System Monitoring Dashboard
  - name: 'ManufactureX System'
    orgId: 1
    folder: 'System Monitoring'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/system

  # Application Monitoring Dashboard
  - name: 'ManufactureX Application'
    orgId: 1
    folder: 'Application Monitoring'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/application

  # Business Intelligence Dashboard
  - name: 'ManufactureX Business'
    orgId: 1
    folder: 'Business Intelligence'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/business
