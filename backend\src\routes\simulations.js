/**
 * Simulation Routes
 * API endpoints for managing manufacturing simulations
 */

const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const simulationController = require('../controllers/simulationController');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Simulation:
 *       type: object
 *       required:
 *         - name
 *         - processType
 *         - parameters
 *       properties:
 *         id:
 *           type: string
 *           description: Unique simulation identifier
 *         name:
 *           type: string
 *           description: Simulation name
 *         processType:
 *           type: string
 *           enum: [assembly, machining, packaging, quality_control, material_handling, welding, painting, injection_molding]
 *           description: Type of manufacturing process
 *         parameters:
 *           type: object
 *           description: Simulation parameters
 *         status:
 *           type: string
 *           enum: [created, initializing, running, paused, stopped, completed, error]
 *           description: Current simulation status
 *         createdAt:
 *           type: string
 *           format: date-time
 *         startedAt:
 *           type: string
 *           format: date-time
 *         completedAt:
 *           type: string
 *           format: date-time
 *         progress:
 *           type: number
 *           minimum: 0
 *           maximum: 1
 *           description: Simulation progress (0-1)
 *         metrics:
 *           type: object
 *           description: Current performance metrics
 */

/**
 * @swagger
 * /api/simulations:
 *   get:
 *     summary: Get all simulations
 *     tags: [Simulations]
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by simulation status
 *       - in: query
 *         name: processType
 *         schema:
 *           type: string
 *         description: Filter by process type
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of simulations to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of simulations to skip
 *     responses:
 *       200:
 *         description: List of simulations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 simulations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Simulation'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 offset:
 *                   type: integer
 */
router.get('/', [
  query('status').optional().isIn(['created', 'initializing', 'running', 'paused', 'stopped', 'completed', 'error']),
  query('processType').optional().isIn(['assembly', 'machining', 'packaging', 'quality_control', 'material_handling', 'welding', 'painting', 'injection_molding']),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('offset').optional().isInt({ min: 0 }).toInt(),
  validateRequest
], simulationController.getAllSimulations);

/**
 * @swagger
 * /api/simulations:
 *   post:
 *     summary: Create a new simulation
 *     tags: [Simulations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - processType
 *               - parameters
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 255
 *               processType:
 *                 type: string
 *                 enum: [assembly, machining, packaging, quality_control, material_handling, welding, painting, injection_molding]
 *               parameters:
 *                 type: object
 *               duration:
 *                 type: integer
 *                 minimum: 1
 *               stepSize:
 *                 type: number
 *                 minimum: 0.1
 *               autoOptimize:
 *                 type: boolean
 *               realTime:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Simulation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Simulation'
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/', [
  body('name').trim().isLength({ min: 1, max: 255 }).withMessage('Name must be between 1 and 255 characters'),
  body('processType').isIn(['assembly', 'machining', 'packaging', 'quality_control', 'material_handling', 'welding', 'painting', 'injection_molding']),
  body('parameters').isObject().withMessage('Parameters must be an object'),
  body('duration').optional().isInt({ min: 1 }).withMessage('Duration must be a positive integer'),
  body('stepSize').optional().isFloat({ min: 0.1 }).withMessage('Step size must be at least 0.1'),
  body('autoOptimize').optional().isBoolean(),
  body('realTime').optional().isBoolean(),
  validateRequest
], simulationController.createSimulation);

/**
 * @swagger
 * /api/simulations/{id}:
 *   get:
 *     summary: Get simulation by ID
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       200:
 *         description: Simulation details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Simulation'
 *       404:
 *         description: Simulation not found
 */
router.get('/:id', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.getSimulationById);

/**
 * @swagger
 * /api/simulations/{id}:
 *   put:
 *     summary: Update simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 255
 *               parameters:
 *                 type: object
 *               duration:
 *                 type: integer
 *                 minimum: 1
 *               stepSize:
 *                 type: number
 *                 minimum: 0.1
 *               autoOptimize:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Simulation updated successfully
 *       404:
 *         description: Simulation not found
 *       400:
 *         description: Invalid request data
 */
router.put('/:id', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  body('name').optional().trim().isLength({ min: 1, max: 255 }),
  body('parameters').optional().isObject(),
  body('duration').optional().isInt({ min: 1 }),
  body('stepSize').optional().isFloat({ min: 0.1 }),
  body('autoOptimize').optional().isBoolean(),
  validateRequest
], simulationController.updateSimulation);

/**
 * @swagger
 * /api/simulations/{id}:
 *   delete:
 *     summary: Delete simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       204:
 *         description: Simulation deleted successfully
 *       404:
 *         description: Simulation not found
 */
router.delete('/:id', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.deleteSimulation);

/**
 * @swagger
 * /api/simulations/{id}/start:
 *   post:
 *     summary: Start simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       200:
 *         description: Simulation started successfully
 *       404:
 *         description: Simulation not found
 *       400:
 *         description: Simulation cannot be started
 */
router.post('/:id/start', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.startSimulation);

/**
 * @swagger
 * /api/simulations/{id}/stop:
 *   post:
 *     summary: Stop simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       200:
 *         description: Simulation stopped successfully
 *       404:
 *         description: Simulation not found
 */
router.post('/:id/stop', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.stopSimulation);

/**
 * @swagger
 * /api/simulations/{id}/pause:
 *   post:
 *     summary: Pause simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       200:
 *         description: Simulation paused successfully
 *       404:
 *         description: Simulation not found
 */
router.post('/:id/pause', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.pauseSimulation);

/**
 * @swagger
 * /api/simulations/{id}/resume:
 *   post:
 *     summary: Resume simulation
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *     responses:
 *       200:
 *         description: Simulation resumed successfully
 *       404:
 *         description: Simulation not found
 */
router.post('/:id/resume', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  validateRequest
], simulationController.resumeSimulation);

/**
 * @swagger
 * /api/simulations/{id}/metrics:
 *   get:
 *     summary: Get simulation metrics
 *     tags: [Simulations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Simulation ID
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [1h, 6h, 24h, 7d, 30d]
 *           default: 1h
 *         description: Time range for metrics
 *     responses:
 *       200:
 *         description: Simulation metrics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 simulationId:
 *                   type: string
 *                 timeRange:
 *                   type: string
 *                 metrics:
 *                   type: object
 *                 history:
 *                   type: array
 *                   items:
 *                     type: object
 *       404:
 *         description: Simulation not found
 */
router.get('/:id/metrics', [
  param('id').isUUID().withMessage('Invalid simulation ID'),
  query('timeRange').optional().isIn(['1h', '6h', '24h', '7d', '30d']),
  validateRequest
], simulationController.getSimulationMetrics);

module.exports = router;
