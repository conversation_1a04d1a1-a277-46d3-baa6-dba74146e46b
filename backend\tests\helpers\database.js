/**
 * Database Test Helpers
 * Utilities for setting up and managing test database
 */

const { Sequelize } = require('sequelize');
const config = require('../../src/config/config');

let sequelize;
let testDbName = 'manufacturex_test';

/**
 * Setup test database
 */
async function setupTestDB() {
  try {
    // Create test database connection
    sequelize = new Sequelize({
      dialect: 'postgres',
      host: config.database.host,
      port: config.database.port,
      username: config.database.username,
      password: config.database.password,
      database: testDbName,
      logging: false, // Disable logging during tests
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    });

    // Test connection
    await sequelize.authenticate();
    console.log('Test database connection established');

    // Create test tables (in a real app, you'd run migrations)
    await createTestTables();
    
    return sequelize;
  } catch (error) {
    console.error('Failed to setup test database:', error);
    throw error;
  }
}

/**
 * Cleanup test database
 */
async function cleanupTestDB() {
  try {
    if (sequelize) {
      // Drop all test tables
      await dropTestTables();
      
      // Close connection
      await sequelize.close();
      console.log('Test database connection closed');
    }
  } catch (error) {
    console.error('Failed to cleanup test database:', error);
    throw error;
  }
}

/**
 * Create test tables
 */
async function createTestTables() {
  try {
    // Users table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Simulations table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS simulations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        process_type VARCHAR(100) NOT NULL,
        parameters JSONB NOT NULL,
        status VARCHAR(50) DEFAULT 'created',
        progress DECIMAL(5,2) DEFAULT 0.00,
        user_id UUID REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        started_at TIMESTAMP,
        completed_at TIMESTAMP
      )
    `);

    // Digital Twins table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS digital_twins (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        twin_type VARCHAR(100) NOT NULL,
        description TEXT,
        physical_asset_id VARCHAR(255),
        parameters JSONB,
        status VARCHAR(50) DEFAULT 'active',
        user_id UUID REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Metrics table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS metrics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        simulation_id UUID REFERENCES simulations(id),
        metric_name VARCHAR(100) NOT NULL,
        metric_value DECIMAL(10,4) NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata JSONB
      )
    `);

    console.log('Test tables created successfully');
  } catch (error) {
    console.error('Failed to create test tables:', error);
    throw error;
  }
}

/**
 * Drop test tables
 */
async function dropTestTables() {
  try {
    await sequelize.query('DROP TABLE IF EXISTS metrics CASCADE');
    await sequelize.query('DROP TABLE IF EXISTS digital_twins CASCADE');
    await sequelize.query('DROP TABLE IF EXISTS simulations CASCADE');
    await sequelize.query('DROP TABLE IF EXISTS users CASCADE');
    
    console.log('Test tables dropped successfully');
  } catch (error) {
    console.error('Failed to drop test tables:', error);
    throw error;
  }
}

/**
 * Clear all test data
 */
async function clearTestData() {
  try {
    await sequelize.query('TRUNCATE TABLE metrics CASCADE');
    await sequelize.query('TRUNCATE TABLE digital_twins CASCADE');
    await sequelize.query('TRUNCATE TABLE simulations CASCADE');
    await sequelize.query('TRUNCATE TABLE users CASCADE');
    
    console.log('Test data cleared successfully');
  } catch (error) {
    console.error('Failed to clear test data:', error);
    throw error;
  }
}

/**
 * Create test user
 */
async function createTestUser(userData = {}) {
  const defaultUser = {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx/L/jG.', // hashed 'password'
    role: 'user'
  };

  const user = { ...defaultUser, ...userData };

  const [result] = await sequelize.query(`
    INSERT INTO users (name, email, password, role)
    VALUES (:name, :email, :password, :role)
    RETURNING *
  `, {
    replacements: user,
    type: sequelize.QueryTypes.SELECT
  });

  return result;
}

/**
 * Create test simulation
 */
async function createTestSimulation(userId, simulationData = {}) {
  const defaultSimulation = {
    name: `Test Simulation ${Date.now()}`,
    process_type: 'assembly',
    parameters: JSON.stringify({
      cycleTime: 30,
      capacity: 1000,
      efficiency: 0.85
    }),
    status: 'created',
    progress: 0.00,
    user_id: userId
  };

  const simulation = { ...defaultSimulation, ...simulationData };

  const [result] = await sequelize.query(`
    INSERT INTO simulations (name, process_type, parameters, status, progress, user_id)
    VALUES (:name, :process_type, :parameters, :status, :progress, :user_id)
    RETURNING *
  `, {
    replacements: simulation,
    type: sequelize.QueryTypes.SELECT
  });

  return result;
}

/**
 * Create test digital twin
 */
async function createTestDigitalTwin(userId, twinData = {}) {
  const defaultTwin = {
    name: `Test Digital Twin ${Date.now()}`,
    twin_type: 'machine',
    description: 'Test digital twin',
    physical_asset_id: `asset_${Date.now()}`,
    parameters: JSON.stringify({
      max_temperature: 80.0,
      max_vibration: 2.0
    }),
    status: 'active',
    user_id: userId
  };

  const twin = { ...defaultTwin, ...twinData };

  const [result] = await sequelize.query(`
    INSERT INTO digital_twins (name, twin_type, description, physical_asset_id, parameters, status, user_id)
    VALUES (:name, :twin_type, :description, :physical_asset_id, :parameters, :status, :user_id)
    RETURNING *
  `, {
    replacements: twin,
    type: sequelize.QueryTypes.SELECT
  });

  return result;
}

/**
 * Get test database connection
 */
function getTestDB() {
  return sequelize;
}

/**
 * Execute raw SQL query
 */
async function executeQuery(sql, replacements = {}) {
  return await sequelize.query(sql, {
    replacements,
    type: sequelize.QueryTypes.SELECT
  });
}

module.exports = {
  setupTestDB,
  cleanupTestDB,
  clearTestData,
  createTestUser,
  createTestSimulation,
  createTestDigitalTwin,
  getTestDB,
  executeQuery
};
