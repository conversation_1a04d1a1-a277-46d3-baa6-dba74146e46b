# ManufactureX - Manufacturing Simulation Engine
# Git ignore file for all project components

# =============================================================================
# ENVIRONMENT AND SECRETS
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.key
*.pem
*.p12
*.pfx
secrets/
config/secrets/

# =============================================================================
# NODE.JS / NPM
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnp
.pnp.js

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =============================================================================
# PYTHON
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# REACT / FRONTEND
# =============================================================================
# Production build
build/
dist/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# DATABASES
# =============================================================================
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# PostgreSQL
*.sql
*.dump
*.backup

# MongoDB
*.bson

# =============================================================================
# LOGS
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# =============================================================================
# DOCKER
# =============================================================================
# Docker volumes
docker-volumes/
.docker/

# Docker Compose override files
docker-compose.override.yml
docker-compose.override.yaml

# =============================================================================
# UPLOADS AND USER DATA
# =============================================================================
uploads/
user-data/
temp/
tmp/
cache/
.cache/

# =============================================================================
# MACHINE LEARNING MODELS
# =============================================================================
models/
*.h5
*.pkl
*.joblib
*.model
*.weights
checkpoints/

# =============================================================================
# DATA FILES
# =============================================================================
data/
datasets/
*.csv
*.json
*.xml
*.xlsx
*.xls
*.parquet

# Exclude example data
!data/examples/
!**/sample-data/

# =============================================================================
# BACKUP FILES
# =============================================================================
backups/
*.backup
*.bak
*.old
*.orig
*.swp
*.swo
*~

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# CERTIFICATES AND KEYS
# =============================================================================
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx
*.jks
*.keystore

# =============================================================================
# MONITORING AND METRICS
# =============================================================================
prometheus/
grafana/data/
influxdb/data/

# =============================================================================
# TESTING
# =============================================================================
# Test results
test-results/
coverage/
.nyc_output/
junit.xml

# =============================================================================
# DOCUMENTATION
# =============================================================================
# Generated documentation
docs/_build/
docs/build/
site/

# =============================================================================
# MISCELLANEOUS
# =============================================================================
# Thumbnails
*.thumb

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Exclude specific files but keep directory structure
**/logs/.gitkeep
**/uploads/.gitkeep
**/temp/.gitkeep
**/data/.gitkeep
