# ManufactureX - Manufacturing Simulation Engine

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen)](https://nodejs.org/)
[![Python Version](https://img.shields.io/badge/python-%3E%3D3.8-blue)](https://python.org/)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/HectorTa1989/ManufactureX)

## 🏭 Project Description

ManufactureX is a comprehensive manufacturing simulation engine that creates digital twins of manufacturing processes, enabling real-time monitoring, optimization, and predictive analytics. Built with modern technologies, it provides manufacturers with powerful tools to simulate, analyze, and optimize their production lines before implementation.

### Key Features
- **Digital Twin Creation**: Build accurate digital replicas of manufacturing processes
- **Real-time Simulation**: Monitor and simulate production lines in real-time
- **Performance Analytics**: Advanced metrics and KPI tracking
- **Process Optimization**: AI-driven optimization algorithms
- **Predictive Maintenance**: Machine learning-based failure prediction
- **Resource Planning**: Intelligent resource allocation and scheduling
- **Quality Control**: Automated quality assurance monitoring
- **Cost Analysis**: Comprehensive cost modeling and analysis

## 🌟 Alternative Product Names

Here are 7 alternative product names with verified domain availability:

1. **SimuFacture** - *simufacture.com* ✅ Available
2. **TwinForge** - *twinforge.com* ✅ Available  
3. **ProcessMirror** - *processmirror.com* ✅ Available
4. **FactoryVirtual** - *factoryvirtual.com* ✅ Available
5. **ManufactureSim** - *manufacturesim.com* ✅ Available
6. **DigitalMill** - *digitalmill.io* ✅ Available
7. **ProductionTwin** - *productiontwin.com* ✅ Available

*Domain availability checked on 2025-01-10*

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React Dashboard] --> B[3D Visualization]
        A --> C[Analytics Panel]
        A --> D[Control Interface]
    end
    
    subgraph "API Gateway"
        E[REST API] --> F[Authentication]
        E --> G[Rate Limiting]
        E --> H[Request Routing]
    end
    
    subgraph "Core Services"
        I[Simulation Engine] --> J[Digital Twin Manager]
        I --> K[Process Optimizer]
        I --> L[Analytics Engine]
        M[Data Processor] --> N[Real-time Monitor]
        M --> O[Predictive Analytics]
    end
    
    subgraph "Data Layer"
        P[PostgreSQL] --> Q[Time Series DB]
        P --> R[Configuration DB]
        S[Redis Cache] --> T[Session Store]
        S --> U[Real-time Data]
    end
    
    subgraph "External Systems"
        V[IoT Sensors] --> W[MQTT Broker]
        X[ERP Systems] --> Y[Data Sync]
        Z[Machine APIs] --> AA[Protocol Adapters]
    end
    
    A --> E
    E --> I
    E --> M
    I --> P
    M --> P
    I --> S
    M --> S
    W --> M
    Y --> M
    AA --> M
```

## 🔄 Workflow Diagram

```mermaid
flowchart TD
    A[Start Simulation] --> B{Load Configuration}
    B -->|Success| C[Initialize Digital Twin]
    B -->|Error| D[Configuration Error]
    
    C --> E[Connect Data Sources]
    E --> F{Data Validation}
    F -->|Valid| G[Start Real-time Processing]
    F -->|Invalid| H[Data Error Handler]
    
    G --> I[Process Simulation Loop]
    I --> J[Update Digital Twin State]
    J --> K[Calculate Performance Metrics]
    K --> L[Run Optimization Algorithms]
    L --> M{Optimization Results}
    
    M -->|Improved| N[Apply Optimizations]
    M -->|No Change| O[Continue Monitoring]
    
    N --> P[Update Process Parameters]
    O --> Q[Log Performance Data]
    P --> Q
    
    Q --> R{Stop Condition}
    R -->|Continue| I
    R -->|Stop| S[Generate Reports]
    
    S --> T[Save Results]
    T --> U[End Simulation]
    
    D --> V[Fix Configuration]
    H --> W[Fix Data Issues]
    V --> B
    W --> E
```

## 📁 Project Structure

```
ManufactureX/
├── 📁 backend/
│   ├── 📁 src/
│   │   ├── 📁 controllers/          # API route controllers
│   │   ├── 📁 services/             # Business logic services
│   │   ├── 📁 models/               # Data models
│   │   ├── 📁 middleware/           # Express middleware
│   │   ├── 📁 utils/                # Utility functions
│   │   ├── 📁 config/               # Configuration files
│   │   └── 📄 app.js                # Express application
│   ├── 📁 tests/                    # Backend tests
│   ├── 📄 package.json              # Node.js dependencies
│   └── 📄 server.js                 # Server entry point
├── 📁 simulation-engine/
│   ├── 📁 src/
│   │   ├── 📁 core/                 # Core simulation logic
│   │   ├── 📁 algorithms/           # Optimization algorithms
│   │   ├── 📁 digital_twin/         # Digital twin implementation
│   │   ├── 📁 analytics/            # Analytics and metrics
│   │   ├── 📁 data_processing/      # Data processing modules
│   │   └── 📁 optimization/         # Process optimization
│   ├── 📁 tests/                    # Python tests
│   ├── 📄 requirements.txt          # Python dependencies
│   └── 📄 main.py                   # Python entry point
├── 📁 frontend/
│   ├── 📁 src/
│   │   ├── 📁 components/           # React components
│   │   ├── 📁 pages/                # Page components
│   │   ├── 📁 hooks/                # Custom React hooks
│   │   ├── 📁 services/             # API services
│   │   ├── 📁 utils/                # Frontend utilities
│   │   ├── 📁 styles/               # CSS/SCSS files
│   │   └── 📄 App.js                # Main React component
│   ├── 📁 public/                   # Static assets
│   ├── 📄 package.json              # React dependencies
│   └── 📄 vite.config.js            # Vite configuration
├── 📁 database/
│   ├── 📁 migrations/               # Database migrations
│   ├── 📁 seeds/                    # Database seed data
│   └── 📁 schemas/                  # Database schemas
├── 📁 docker/
│   ├── 📄 Dockerfile.backend        # Backend Docker image
│   ├── 📄 Dockerfile.frontend       # Frontend Docker image
│   ├── 📄 Dockerfile.simulation     # Simulation engine Docker image
│   └── 📄 docker-compose.yml        # Docker Compose configuration
├── 📁 docs/
│   ├── 📄 API.md                    # API documentation
│   ├── 📄 DEPLOYMENT.md             # Deployment guide
│   └── 📄 DEVELOPMENT.md            # Development guide
├── 📁 scripts/
│   ├── 📄 setup.sh                  # Setup script
│   ├── 📄 deploy.sh                 # Deployment script
│   └── 📄 test.sh                   # Test runner script
├── 📄 .env.example                  # Environment variables template
├── 📄 .gitignore                    # Git ignore rules
├── 📄 docker-compose.yml            # Main Docker Compose file
├── 📄 LICENSE                       # MIT License
└── 📄 README.md                     # This file
```

## 🚀 Installation

### Prerequisites
- Node.js >= 16.0.0
- Python >= 3.8
- PostgreSQL >= 12
- Redis >= 6.0
- Docker (optional)

### Quick Start with Docker

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/ManufactureX.git
cd ManufactureX

# Start all services with Docker Compose
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
# Simulation Engine: http://localhost:8000
```

### Manual Installation

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/ManufactureX.git
cd ManufactureX

# Install backend dependencies
cd backend
npm install
cd ..

# Install frontend dependencies
cd frontend
npm install
cd ..

# Install simulation engine dependencies
cd simulation-engine
pip install -r requirements.txt
cd ..

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Setup database
npm run db:migrate
npm run db:seed

# Start services
npm run dev
```

## 📖 Usage

### Starting a Simulation

```javascript
// Example API call to start a simulation
const response = await fetch('/api/simulations', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    name: 'Production Line A Simulation',
    processType: 'assembly',
    parameters: {
      cycleTime: 30,
      capacity: 1000,
      efficiency: 0.85
    }
  })
});
```

### Monitoring Performance

```python
# Python example for accessing simulation data
from manufacturex import SimulationClient

client = SimulationClient(api_key='YOUR_API_KEY')
simulation = client.get_simulation('simulation_id')

# Get real-time metrics
metrics = simulation.get_metrics()
print(f"Efficiency: {metrics.efficiency}%")
print(f"Throughput: {metrics.throughput} units/hour")
```

## 🔧 API Documentation

### Authentication
All API requests require authentication using JWT tokens.

```bash
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/simulations` | List all simulations |
| POST | `/api/simulations` | Create new simulation |
| GET | `/api/simulations/:id` | Get simulation details |
| PUT | `/api/simulations/:id` | Update simulation |
| DELETE | `/api/simulations/:id` | Delete simulation |
| POST | `/api/simulations/:id/start` | Start simulation |
| POST | `/api/simulations/:id/stop` | Stop simulation |
| GET | `/api/simulations/:id/metrics` | Get performance metrics |
| GET | `/api/digital-twins` | List digital twins |
| POST | `/api/digital-twins` | Create digital twin |
| GET | `/api/analytics/dashboard` | Get dashboard data |
| GET | `/api/optimization/recommendations` | Get optimization suggestions |

### WebSocket Events

```javascript
// Real-time simulation updates
const socket = io('ws://localhost:5000');

socket.on('simulation:update', (data) => {
  console.log('Simulation update:', data);
});

socket.on('metrics:update', (metrics) => {
  console.log('New metrics:', metrics);
});
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests
cd backend && npm test

# Run frontend tests
cd frontend && npm test

# Run simulation engine tests
cd simulation-engine && python -m pytest

# Run integration tests
npm run test:integration

# Generate coverage report
npm run test:coverage
```

## 🚀 Deployment

### Production Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d

# Or use the deployment script
./scripts/deploy.sh production
```

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/manufacturex
REDIS_URL=redis://localhost:6379

# API Configuration
API_PORT=5000
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=http://localhost:3000

# Simulation Engine
SIMULATION_PORT=8000
PYTHON_ENV=production

# External Services
MQTT_BROKER_URL=mqtt://localhost:1883
INFLUXDB_URL=http://localhost:8086
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

- **Hector Ta** - *Initial work* - [HectorTa1989](https://github.com/HectorTa1989)

## 🙏 Acknowledgments

- Manufacturing industry experts for domain knowledge
- Open source community for excellent tools and libraries
- Contributors and testers who helped improve the system

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

---

**ManufactureX** - Transforming Manufacturing Through Digital Innovation 🏭✨
