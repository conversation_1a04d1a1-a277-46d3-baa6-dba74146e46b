{"date": "Thu Jul 10 2025 23:56:57 GMT+0700 (Indochina Time)", "environment": "development", "error": {"code": "MODULE_NOT_FOUND", "requireStack": ["D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js", "D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\server.js"]}, "exception": true, "level": "error", "message": "uncaughtException: Cannot find module './routes/auth'\nRequire stack:\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\server.js\nError: Cannot find module './routes/auth'\nRequire stack:\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js:20:20)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)", "os": {"loadavg": [0, 0, 0], "uptime": 349195.703}, "process": {"argv": ["C:\\Program Files\\nodejs\\node.exe", "D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\server.js"], "cwd": "D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend", "execPath": "C:\\Program Files\\nodejs\\node.exe", "gid": null, "memoryUsage": {"arrayBuffers": 16795, "external": 2295263, "heapTotal": 32137216, "heapUsed": 16109512, "rss": 53874688}, "pid": 28236, "uid": null, "version": "v20.19.0"}, "service": "manufacturex-backend", "stack": "Error: Cannot find module './routes/auth'\nRequire stack:\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js\n- D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js:20:20)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)", "timestamp": "2025-07-10 23:56:57", "trace": [{"column": 15, "file": "node:internal/modules/cjs/loader", "function": "Module._resolveFilename", "line": 1212, "method": "_resolveFilename", "native": false}, {"column": 27, "file": "node:internal/modules/cjs/loader", "function": "Module._load", "line": 1043, "method": "_load", "native": false}, {"column": 19, "file": "node:internal/modules/cjs/loader", "function": "Module.require", "line": 1298, "method": "require", "native": false}, {"column": 18, "file": "node:internal/modules/helpers", "function": "require", "line": 182, "method": null, "native": false}, {"column": 20, "file": "D:\\Project\\Digital twin\\ManufactureX_Manufacturing Simulation Engine_AugmentCode\\backend\\src\\app.js", "function": null, "line": 20, "method": null, "native": false}, {"column": 14, "file": "node:internal/modules/cjs/loader", "function": "Module._compile", "line": 1529, "method": "_compile", "native": false}, {"column": 10, "file": "node:internal/modules/cjs/loader", "function": "Module._extensions..js", "line": 1613, "method": ".js", "native": false}, {"column": 32, "file": "node:internal/modules/cjs/loader", "function": "Module.load", "line": 1275, "method": "load", "native": false}, {"column": 12, "file": "node:internal/modules/cjs/loader", "function": "Module._load", "line": 1096, "method": "_load", "native": false}, {"column": 19, "file": "node:internal/modules/cjs/loader", "function": "Module.require", "line": 1298, "method": "require", "native": false}], "version": "1.0.0"}