/**
 * Authentication Test Helpers
 * Utilities for testing authentication and authorization
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const config = require('../../src/config/config');
const { createTestUser } = require('./database');

/**
 * Create test user with hashed password
 */
async function createTestUserWithAuth(userData = {}) {
  const password = userData.password || 'TestPassword123!';
  const hashedPassword = await bcrypt.hash(password, 12);
  
  const user = await createTestUser({
    ...userData,
    password: hashedPassword
  });
  
  // Return user with plain password for testing
  return {
    ...user,
    plainPassword: password
  };
}

/**
 * Generate JWT token for test user
 */
function generateTestToken(user) {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role || 'user'
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn
  });
}

/**
 * Generate expired JWT token
 */
function generateExpiredToken(user) {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role || 'user'
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: '-1h' // Expired 1 hour ago
  });
}

/**
 * Generate invalid JWT token
 */
function generateInvalidToken() {
  return jwt.sign({ id: 'invalid' }, 'wrong-secret');
}

/**
 * Get auth token for test user
 */
async function getAuthToken(user) {
  if (!user) {
    user = await createTestUserWithAuth();
  }
  return generateTestToken(user);
}

/**
 * Create admin user for testing
 */
async function createTestAdmin(userData = {}) {
  return await createTestUserWithAuth({
    name: 'Test Admin',
    email: `admin${Date.now()}@example.com`,
    role: 'admin',
    ...userData
  });
}

/**
 * Create regular user for testing
 */
async function createTestRegularUser(userData = {}) {
  return await createTestUserWithAuth({
    name: 'Test User',
    email: `user${Date.now()}@example.com`,
    role: 'user',
    ...userData
  });
}

/**
 * Mock authentication middleware
 */
function mockAuthMiddleware(user = null) {
  return (req, res, next) => {
    if (user) {
      req.user = user;
      res.locals.user = user;
    }
    next();
  };
}

/**
 * Mock admin middleware
 */
function mockAdminMiddleware() {
  return (req, res, next) => {
    req.user = {
      id: 'admin-id',
      email: '<EMAIL>',
      role: 'admin'
    };
    res.locals.user = req.user;
    next();
  };
}

/**
 * Create authorization header
 */
function createAuthHeader(token) {
  return `Bearer ${token}`;
}

/**
 * Verify JWT token
 */
function verifyTestToken(token) {
  try {
    return jwt.verify(token, config.jwt.secret);
  } catch (error) {
    return null;
  }
}

/**
 * Create test API key
 */
function createTestApiKey() {
  return `test_api_key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Mock request with authentication
 */
function mockAuthenticatedRequest(user, additionalData = {}) {
  return {
    user,
    headers: {
      authorization: `Bearer ${generateTestToken(user)}`,
      'content-type': 'application/json',
      ...additionalData.headers
    },
    ip: '127.0.0.1',
    get: jest.fn((header) => {
      const headers = {
        'user-agent': 'test-agent',
        'content-type': 'application/json',
        ...additionalData.headers
      };
      return headers[header.toLowerCase()];
    }),
    ...additionalData
  };
}

/**
 * Mock response object
 */
function mockResponse() {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis(),
    locals: {}
  };
  return res;
}

/**
 * Test authentication scenarios
 */
const authScenarios = {
  validUser: async () => {
    const user = await createTestRegularUser();
    const token = generateTestToken(user);
    return { user, token };
  },
  
  adminUser: async () => {
    const user = await createTestAdmin();
    const token = generateTestToken(user);
    return { user, token };
  },
  
  expiredToken: async () => {
    const user = await createTestRegularUser();
    const token = generateExpiredToken(user);
    return { user, token };
  },
  
  invalidToken: () => {
    const token = generateInvalidToken();
    return { user: null, token };
  },
  
  noToken: () => {
    return { user: null, token: null };
  }
};

/**
 * Test login credentials
 */
const testCredentials = {
  valid: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  
  invalidEmail: {
    email: 'invalid-email',
    password: 'TestPassword123!'
  },
  
  invalidPassword: {
    email: '<EMAIL>',
    password: 'wrong'
  },
  
  missing: {
    email: '',
    password: ''
  }
};

/**
 * Password validation helpers
 */
const passwordHelpers = {
  hash: async (password) => {
    return await bcrypt.hash(password, 12);
  },
  
  compare: async (password, hash) => {
    return await bcrypt.compare(password, hash);
  },
  
  generateStrong: () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  },
  
  generateWeak: () => {
    return 'weak';
  }
};

module.exports = {
  createTestUserWithAuth,
  generateTestToken,
  generateExpiredToken,
  generateInvalidToken,
  getAuthToken,
  createTestAdmin,
  createTestRegularUser,
  mockAuthMiddleware,
  mockAdminMiddleware,
  createAuthHeader,
  verifyTestToken,
  createTestApiKey,
  mockAuthenticatedRequest,
  mockResponse,
  authScenarios,
  testCredentials,
  passwordHelpers
};
