/**
 * Application Configuration
 * Centralized configuration management
 */

require('dotenv').config();

const config = {
  // Environment
  env: process.env.NODE_ENV || 'development',
  
  // Server
  port: parseInt(process.env.PORT, 10) || 5000,
  host: process.env.HOST || '0.0.0.0',
  
  // CORS
  corsOrigin: process.env.CORS_ORIGIN || '*',
  
  // Database
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    name: process.env.DB_NAME || 'manufacturex',
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: parseInt(process.env.DB_POOL_MAX, 10) || 10,
      min: parseInt(process.env.DB_POOL_MIN, 10) || 0,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE, 10) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE, 10) || 10000
    }
  },
  
  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || null,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'manufacturex:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },
  
  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },
  
  // Simulation Engine
  simulationEngine: {
    host: process.env.SIMULATION_ENGINE_HOST || 'localhost',
    port: parseInt(process.env.SIMULATION_ENGINE_PORT, 10) || 8000,
    timeout: parseInt(process.env.SIMULATION_ENGINE_TIMEOUT, 10) || 30000,
    retries: parseInt(process.env.SIMULATION_ENGINE_RETRIES, 10) || 3
  },
  
  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10 * 1024 * 1024, // 10MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,application/pdf,text/csv').split(','),
    uploadDir: process.env.UPLOAD_DIR || './uploads'
  },
  
  // Email
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT, 10) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || '',
    password: process.env.EMAIL_PASSWORD || '',
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },
  
  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
    datePattern: process.env.LOG_DATE_PATTERN || 'YYYY-MM-DD'
  },
  
  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100, // limit each IP to 100 requests per windowMs
    message: process.env.RATE_LIMIT_MESSAGE || 'Too many requests from this IP, please try again later.'
  },
  
  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
    cookieMaxAge: parseInt(process.env.COOKIE_MAX_AGE, 10) || 24 * 60 * 60 * 1000 // 24 hours
  },
  
  // External APIs
  externalApis: {
    weatherApi: {
      key: process.env.WEATHER_API_KEY || '',
      baseUrl: process.env.WEATHER_API_BASE_URL || 'https://api.openweathermap.org/data/2.5'
    },
    exchangeRateApi: {
      key: process.env.EXCHANGE_RATE_API_KEY || '',
      baseUrl: process.env.EXCHANGE_RATE_API_BASE_URL || 'https://api.exchangerate-api.com/v4/latest'
    }
  },
  
  // Monitoring
  monitoring: {
    enabled: process.env.MONITORING_ENABLED === 'true',
    prometheusPort: parseInt(process.env.PROMETHEUS_PORT, 10) || 9090,
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000 // 30 seconds
  },
  
  // Background Jobs
  jobs: {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT, 10) || 6379,
      password: process.env.REDIS_PASSWORD || null
    },
    concurrency: parseInt(process.env.JOB_CONCURRENCY, 10) || 5,
    removeOnComplete: parseInt(process.env.JOB_REMOVE_ON_COMPLETE, 10) || 10,
    removeOnFail: parseInt(process.env.JOB_REMOVE_ON_FAIL, 10) || 5
  },
  
  // WebSocket
  websocket: {
    enabled: process.env.WEBSOCKET_ENABLED !== 'false',
    cors: {
      origin: process.env.WEBSOCKET_CORS_ORIGIN || '*',
      methods: ['GET', 'POST']
    }
  },
  
  // Development
  development: {
    seedDatabase: process.env.SEED_DATABASE === 'true',
    mockExternalApis: process.env.MOCK_EXTERNAL_APIS === 'true',
    debugMode: process.env.DEBUG_MODE === 'true'
  }
};

// Validation
const requiredEnvVars = [
  'JWT_SECRET',
  'DB_PASSWORD'
];

if (config.env === 'production') {
  requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      throw new Error(`Required environment variable ${envVar} is not set`);
    }
  });
}

// Database URL for Sequelize CLI
config.database.url = `postgres://${config.database.username}:${config.database.password}@${config.database.host}:${config.database.port}/${config.database.name}`;

module.exports = config;
