/**
 * Rate Limiting Middleware
 * Comprehensive rate limiting for API endpoints
 */

const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const redis = require('redis');
const config = require('../config/config');
const logger = require('../utils/logger');
const { RateLimitError } = require('../utils/errors');

// Create Redis client for rate limiting
let redisClient;
try {
  redisClient = redis.createClient({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    db: config.redis.db
  });
  
  redisClient.on('error', (err) => {
    logger.error('Redis rate limiter error:', err);
  });
} catch (error) {
  logger.error('Failed to create Redis client for rate limiting:', error);
}

/**
 * Create rate limiter with custom configuration
 */
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.max,
    message: {
      success: false,
      error: 'Rate Limit Exceeded',
      message: config.rateLimit.message,
      retryAfter: Math.ceil(config.rateLimit.windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    store: redisClient ? new RedisStore({
      sendCommand: (...args) => redisClient.sendCommand(args),
      prefix: 'rl:',
    }) : undefined,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user?.id || req.ip;
    },
    handler: (req, res) => {
      logger.logSecurity('Rate limit exceeded', {
        ip: req.ip,
        userId: req.user?.id,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
        method: req.method
      });
      
      res.status(429).json({
        success: false,
        error: 'Rate Limit Exceeded',
        message: options.message || config.rateLimit.message,
        retryAfter: Math.ceil((options.windowMs || config.rateLimit.windowMs) / 1000),
        timestamp: new Date().toISOString()
      });
    },
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/api/health';
    },
    onLimitReached: (req, res, options) => {
      logger.logSecurity('Rate limit reached', {
        ip: req.ip,
        userId: req.user?.id,
        limit: options.max,
        windowMs: options.windowMs,
        path: req.originalUrl
      });
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
};

/**
 * General API rate limiter
 */
const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many requests from this IP, please try again later.'
});

/**
 * Strict rate limiter for authentication endpoints
 */
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true, // Don't count successful requests
  keyGenerator: (req) => {
    // Use email + IP for login attempts
    const email = req.body?.email || '';
    return `${req.ip}:${email}`;
  }
});

/**
 * Lenient rate limiter for public endpoints
 */
const publicLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // 200 requests per window
  message: 'Too many requests, please try again later.'
});

/**
 * Strict rate limiter for resource-intensive operations
 */
const heavyOperationLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour
  message: 'Too many resource-intensive requests, please try again later.'
});

/**
 * Rate limiter for simulation operations
 */
const simulationLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // 20 simulation operations per 5 minutes
  message: 'Too many simulation requests, please try again later.'
});

/**
 * Rate limiter for file uploads
 */
const uploadLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 uploads per window
  message: 'Too many file uploads, please try again later.'
});

/**
 * Rate limiter for password reset requests
 */
const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset requests per hour
  message: 'Too many password reset requests, please try again later.',
  keyGenerator: (req) => {
    // Use email for password reset attempts
    return req.body?.email || req.ip;
  }
});

/**
 * Rate limiter for API key requests
 */
const apiKeyLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per window for API keys
  message: 'API rate limit exceeded, please try again later.',
  keyGenerator: (req) => {
    // Use API key for rate limiting
    return req.headers['x-api-key'] || req.ip;
  }
});

/**
 * Dynamic rate limiter based on user role
 */
const dynamicRoleLimiter = (req, res, next) => {
  const user = req.user;
  let limiter;

  if (!user) {
    // Anonymous users get strict limits
    limiter = createRateLimiter({
      windowMs: 15 * 60 * 1000,
      max: 50,
      message: 'Please log in for higher rate limits.'
    });
  } else if (user.role === 'admin') {
    // Admins get higher limits
    limiter = createRateLimiter({
      windowMs: 15 * 60 * 1000,
      max: 500,
      message: 'Admin rate limit exceeded.'
    });
  } else if (user.role === 'premium') {
    // Premium users get higher limits
    limiter = createRateLimiter({
      windowMs: 15 * 60 * 1000,
      max: 300,
      message: 'Premium user rate limit exceeded.'
    });
  } else {
    // Regular users get standard limits
    limiter = createRateLimiter({
      windowMs: 15 * 60 * 1000,
      max: 100,
      message: 'User rate limit exceeded.'
    });
  }

  limiter(req, res, next);
};

/**
 * Sliding window rate limiter
 */
const slidingWindowLimiter = (windowMs, maxRequests) => {
  const requests = new Map();

  return (req, res, next) => {
    const key = req.user?.id || req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old requests
    if (requests.has(key)) {
      const userRequests = requests.get(key).filter(time => time > windowStart);
      requests.set(key, userRequests);
    } else {
      requests.set(key, []);
    }

    const userRequests = requests.get(key);

    if (userRequests.length >= maxRequests) {
      logger.logSecurity('Sliding window rate limit exceeded', {
        ip: req.ip,
        userId: req.user?.id,
        requestCount: userRequests.length,
        maxRequests,
        windowMs
      });

      return res.status(429).json({
        success: false,
        error: 'Rate Limit Exceeded',
        message: 'Too many requests in sliding window',
        retryAfter: Math.ceil(windowMs / 1000),
        timestamp: new Date().toISOString()
      });
    }

    // Add current request
    userRequests.push(now);
    next();
  };
};

/**
 * Burst rate limiter - allows short bursts but limits sustained load
 */
const burstLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute
  message: 'Burst rate limit exceeded, please slow down.'
});

/**
 * Custom rate limiter for specific endpoints
 */
const customEndpointLimiter = (endpoint, options) => {
  return createRateLimiter({
    ...options,
    keyGenerator: (req) => {
      return `${endpoint}:${req.user?.id || req.ip}`;
    },
    message: `Rate limit exceeded for ${endpoint}`
  });
};

/**
 * Rate limiter with exponential backoff
 */
const exponentialBackoffLimiter = (baseWindowMs, maxRequests) => {
  const attempts = new Map();

  return (req, res, next) => {
    const key = req.user?.id || req.ip;
    const now = Date.now();

    if (!attempts.has(key)) {
      attempts.set(key, { count: 0, lastAttempt: now, windowMs: baseWindowMs });
    }

    const userAttempts = attempts.get(key);
    const timeSinceLastAttempt = now - userAttempts.lastAttempt;

    // Reset if enough time has passed
    if (timeSinceLastAttempt > userAttempts.windowMs) {
      userAttempts.count = 0;
      userAttempts.windowMs = baseWindowMs;
    }

    if (userAttempts.count >= maxRequests) {
      // Exponential backoff
      userAttempts.windowMs = Math.min(userAttempts.windowMs * 2, 60 * 60 * 1000); // Max 1 hour
      
      logger.logSecurity('Exponential backoff rate limit exceeded', {
        ip: req.ip,
        userId: req.user?.id,
        attempts: userAttempts.count,
        windowMs: userAttempts.windowMs
      });

      return res.status(429).json({
        success: false,
        error: 'Rate Limit Exceeded',
        message: 'Too many requests, exponential backoff applied',
        retryAfter: Math.ceil(userAttempts.windowMs / 1000),
        timestamp: new Date().toISOString()
      });
    }

    userAttempts.count++;
    userAttempts.lastAttempt = now;
    next();
  };
};

module.exports = {
  generalLimiter,
  authLimiter,
  publicLimiter,
  heavyOperationLimiter,
  simulationLimiter,
  uploadLimiter,
  passwordResetLimiter,
  apiKeyLimiter,
  dynamicRoleLimiter,
  slidingWindowLimiter,
  burstLimiter,
  customEndpointLimiter,
  exponentialBackoffLimiter,
  createRateLimiter
};
