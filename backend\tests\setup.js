/**
 * Test Setup Configuration
 * Global test setup for Jest testing framework
 */

const { setupTestDB, cleanupTestDB } = require('./helpers/database');
const { setupTestRedis, cleanupTestRedis } = require('./helpers/redis');
const logger = require('../src/utils/logger');

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.DB_NAME = 'manufacturex_test';
process.env.REDIS_DB = '1';

// Increase timeout for integration tests
jest.setTimeout(30000);

// Global test setup
beforeAll(async () => {
  try {
    // Setup test database
    await setupTestDB();
    
    // Setup test Redis
    await setupTestRedis();
    
    // Suppress logs during testing
    logger.level = 'error';
    
    console.log('Test environment setup completed');
  } catch (error) {
    console.error('Test setup failed:', error);
    process.exit(1);
  }
});

// Global test cleanup
afterAll(async () => {
  try {
    // Cleanup test database
    await cleanupTestDB();
    
    // Cleanup test Redis
    await cleanupTestRedis();
    
    console.log('Test environment cleanup completed');
  } catch (error) {
    console.error('Test cleanup failed:', error);
  }
});

// Global error handler for unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Mock external services
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn()
  })),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn()
}));

// Mock Redis client
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushdb: jest.fn()
  }))
}));

// Mock Socket.IO
jest.mock('socket.io', () => ({
  Server: jest.fn(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn(() => ({
      emit: jest.fn()
    })),
    close: jest.fn()
  }))
}));

// Mock file system operations
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
    mkdir: jest.fn(),
    rmdir: jest.fn(),
    stat: jest.fn(),
    access: jest.fn()
  }
}));

// Mock email service
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' })
  }))
}));

// Custom matchers
expect.extend({
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },
  
  toBeValidEmail(received) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },
  
  toBeValidDate(received) {
    const date = new Date(received);
    const pass = date instanceof Date && !isNaN(date);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },
  
  toHaveValidationError(received, field) {
    const hasErrors = received.body && received.body.errors && Array.isArray(received.body.errors);
    const hasFieldError = hasErrors && received.body.errors.some(error => error.field === field);
    
    if (hasFieldError) {
      return {
        message: () => `expected response not to have validation error for field ${field}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected response to have validation error for field ${field}`,
        pass: false,
      };
    }
  }
});

// Global test utilities
global.testUtils = {
  // Generate test data
  generateTestUser: () => ({
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'user'
  }),
  
  generateTestSimulation: () => ({
    name: `Test Simulation ${Date.now()}`,
    processType: 'assembly',
    parameters: {
      cycleTime: 30,
      capacity: 1000,
      efficiency: 0.85
    },
    duration: 3600,
    stepSize: 1.0,
    autoOptimize: false,
    realTime: false
  }),
  
  generateTestDigitalTwin: () => ({
    name: `Test Digital Twin ${Date.now()}`,
    twin_type: 'machine',
    description: 'Test digital twin for testing',
    physical_asset_id: `asset_${Date.now()}`,
    parameters: {
      max_temperature: 80.0,
      max_vibration: 2.0,
      rated_speed: 1800.0
    },
    sensors: [
      {
        type: 'temperature',
        location: 'spindle',
        unit: 'celsius'
      }
    ],
    update_frequency: 1.0
  }),
  
  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate random string
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // Generate random number
  randomNumber: (min = 0, max = 100) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
};
