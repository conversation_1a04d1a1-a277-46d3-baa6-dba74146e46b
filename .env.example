# ManufactureX Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# GENERAL SETTINGS
# =============================================================================
NODE_ENV=development
LOG_LEVEL=info

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=manufacturex
DB_USERNAME=postgres
DB_PASSWORD=password123
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DB=0
REDIS_KEY_PREFIX=manufacturex:

# =============================================================================
# BACKEND API CONFIGURATION
# =============================================================================
PORT=5000
HOST=0.0.0.0
CORS_ORIGIN=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-change-in-production
COOKIE_MAX_AGE=86400000

# Security
BCRYPT_ROUNDS=12

# =============================================================================
# SIMULATION ENGINE CONFIGURATION
# =============================================================================
SIMULATION_ENGINE_HOST=localhost
SIMULATION_ENGINE_PORT=8000
SIMULATION_ENGINE_TIMEOUT=30000
SIMULATION_ENGINE_RETRIES=3

# Python Environment
PYTHONPATH=/app/src
SIMULATION_HOST=0.0.0.0
WORKERS=4
DEBUG=false

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_API_URL=http://localhost:5000
VITE_WS_URL=ws://localhost:5000
VITE_APP_NAME=ManufactureX
VITE_APP_VERSION=1.0.0

# =============================================================================
# MQTT BROKER CONFIGURATION
# =============================================================================
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_CLIENT_ID=manufacturex

# =============================================================================
# INFLUXDB CONFIGURATION
# =============================================================================
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=manufacturex-token
INFLUXDB_ORG=manufacturex
INFLUXDB_BUCKET=metrics
INFLUXDB_PASSWORD=influx123

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf,text/csv
UPLOAD_DIR=./uploads

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE=Too many requests from this IP, please try again later.

# Backend API Rate Limits
API_RATE_LIMIT_GENERAL=100
API_RATE_LIMIT_AUTH=5
API_RATE_LIMIT_UPLOAD=10
API_RATE_LIMIT_HEAVY=10
API_RATE_LIMIT_SIMULATION=20

# Simulation Engine Rate Limits
SIMULATION_RATE_LIMIT_REQUESTS=100
SIMULATION_RATE_LIMIT_WINDOW=60
SIMULATION_RATE_LIMIT_HEAVY=5
SIMULATION_RATE_LIMIT_BURST_CAPACITY=50
SIMULATION_RATE_LIMIT_BURST_REFILL=1.0

# Nginx Rate Limits
NGINX_RATE_LIMIT_API=10r/s
NGINX_RATE_LIMIT_LOGIN=1r/s
NGINX_RATE_LIMIT_GENERAL=30r/s
NGINX_RATE_LIMIT_BURST=50

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FILE=logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
LOG_DATE_PATTERN=YYYY-MM-DD

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
MONITORING_ENABLED=true
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# Grafana
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123

# =============================================================================
# EXTERNAL APIS CONFIGURATION
# =============================================================================
WEATHER_API_KEY=your-weather-api-key
WEATHER_API_BASE_URL=https://api.openweathermap.org/data/2.5

EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
EXCHANGE_RATE_API_BASE_URL=https://api.exchangerate-api.com/v4/latest

# =============================================================================
# BACKGROUND JOBS CONFIGURATION
# =============================================================================
JOB_CONCURRENCY=5
JOB_REMOVE_ON_COMPLETE=10
JOB_REMOVE_ON_FAIL=5

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================
WEBSOCKET_ENABLED=true
WEBSOCKET_CORS_ORIGIN=*

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
SEED_DATABASE=true
MOCK_EXTERNAL_APIS=true
DEBUG_MODE=false

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=manufacturex
COMPOSE_FILE=docker-compose.yml

# =============================================================================
# SSL/TLS CONFIGURATION (Production)
# =============================================================================
SSL_CERT_PATH=/etc/ssl/certs/manufacturex.crt
SSL_KEY_PATH=/etc/ssl/private/manufacturex.key
SSL_ENABLED=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=manufacturex-backups
BACKUP_S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
MIXPANEL_TOKEN=your-mixpanel-token

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_REAL_TIME_OPTIMIZATION=true
FEATURE_MACHINE_LEARNING=true
FEATURE_PREDICTIVE_MAINTENANCE=true
FEATURE_DIGITAL_TWINS=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
MAX_CONCURRENT_SIMULATIONS=10
MAX_DATA_POINTS_PER_REQUEST=10000
REQUEST_TIMEOUT=30000
DATABASE_QUERY_TIMEOUT=10000
