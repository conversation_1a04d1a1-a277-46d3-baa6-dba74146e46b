/**
 * Redis Test Helpers
 * Utilities for testing Redis cache operations
 */

const redis = require('redis');
const config = require('../../src/config/config');

let testRedisClient;

/**
 * Setup test Redis connection
 */
async function setupTestRedis() {
  try {
    testRedisClient = redis.createClient({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: 1, // Use different database for testing
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3
    });

    await testRedisClient.connect();
    console.log('Test Redis connection established');
    
    return testRedisClient;
  } catch (error) {
    console.error('Failed to setup test Redis:', error);
    throw error;
  }
}

/**
 * Cleanup test Redis
 */
async function cleanupTestRedis() {
  try {
    if (testRedisClient) {
      // Clear test database
      await testRedisClient.flushDb();
      
      // Disconnect
      await testRedisClient.disconnect();
      console.log('Test Redis connection closed');
    }
  } catch (error) {
    console.error('Failed to cleanup test Redis:', error);
    throw error;
  }
}

/**
 * Clear all test data from Redis
 */
async function clearTestRedisData() {
  try {
    if (testRedisClient) {
      await testRedisClient.flushDb();
      console.log('Test Redis data cleared');
    }
  } catch (error) {
    console.error('Failed to clear test Redis data:', error);
    throw error;
  }
}

/**
 * Set test data in Redis
 */
async function setTestData(key, value, expiration = null) {
  try {
    const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
    
    if (expiration) {
      await testRedisClient.setEx(key, expiration, serializedValue);
    } else {
      await testRedisClient.set(key, serializedValue);
    }
    
    return true;
  } catch (error) {
    console.error('Failed to set test data in Redis:', error);
    throw error;
  }
}

/**
 * Get test data from Redis
 */
async function getTestData(key) {
  try {
    const value = await testRedisClient.get(key);
    
    if (value === null) {
      return null;
    }
    
    // Try to parse as JSON, fallback to string
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  } catch (error) {
    console.error('Failed to get test data from Redis:', error);
    throw error;
  }
}

/**
 * Delete test data from Redis
 */
async function deleteTestData(key) {
  try {
    const result = await testRedisClient.del(key);
    return result > 0;
  } catch (error) {
    console.error('Failed to delete test data from Redis:', error);
    throw error;
  }
}

/**
 * Check if key exists in Redis
 */
async function keyExists(key) {
  try {
    const result = await testRedisClient.exists(key);
    return result === 1;
  } catch (error) {
    console.error('Failed to check key existence in Redis:', error);
    throw error;
  }
}

/**
 * Set expiration for a key
 */
async function setExpiration(key, seconds) {
  try {
    const result = await testRedisClient.expire(key, seconds);
    return result === 1;
  } catch (error) {
    console.error('Failed to set expiration in Redis:', error);
    throw error;
  }
}

/**
 * Get TTL for a key
 */
async function getTTL(key) {
  try {
    return await testRedisClient.ttl(key);
  } catch (error) {
    console.error('Failed to get TTL from Redis:', error);
    throw error;
  }
}

/**
 * Mock Redis client for testing
 */
function createMockRedisClient() {
  const mockData = new Map();
  const mockExpirations = new Map();
  
  return {
    connect: jest.fn().mockResolvedValue(true),
    disconnect: jest.fn().mockResolvedValue(true),
    
    get: jest.fn().mockImplementation(async (key) => {
      // Check if key has expired
      if (mockExpirations.has(key)) {
        const expiration = mockExpirations.get(key);
        if (Date.now() > expiration) {
          mockData.delete(key);
          mockExpirations.delete(key);
          return null;
        }
      }
      return mockData.get(key) || null;
    }),
    
    set: jest.fn().mockImplementation(async (key, value) => {
      mockData.set(key, value);
      return 'OK';
    }),
    
    setEx: jest.fn().mockImplementation(async (key, seconds, value) => {
      mockData.set(key, value);
      mockExpirations.set(key, Date.now() + (seconds * 1000));
      return 'OK';
    }),
    
    del: jest.fn().mockImplementation(async (key) => {
      const existed = mockData.has(key);
      mockData.delete(key);
      mockExpirations.delete(key);
      return existed ? 1 : 0;
    }),
    
    exists: jest.fn().mockImplementation(async (key) => {
      // Check if key has expired
      if (mockExpirations.has(key)) {
        const expiration = mockExpirations.get(key);
        if (Date.now() > expiration) {
          mockData.delete(key);
          mockExpirations.delete(key);
          return 0;
        }
      }
      return mockData.has(key) ? 1 : 0;
    }),
    
    expire: jest.fn().mockImplementation(async (key, seconds) => {
      if (mockData.has(key)) {
        mockExpirations.set(key, Date.now() + (seconds * 1000));
        return 1;
      }
      return 0;
    }),
    
    ttl: jest.fn().mockImplementation(async (key) => {
      if (!mockData.has(key)) {
        return -2; // Key doesn't exist
      }
      
      if (!mockExpirations.has(key)) {
        return -1; // Key exists but has no expiration
      }
      
      const expiration = mockExpirations.get(key);
      const remaining = Math.ceil((expiration - Date.now()) / 1000);
      
      if (remaining <= 0) {
        mockData.delete(key);
        mockExpirations.delete(key);
        return -2;
      }
      
      return remaining;
    }),
    
    flushDb: jest.fn().mockImplementation(async () => {
      mockData.clear();
      mockExpirations.clear();
      return 'OK';
    }),
    
    // Additional methods for testing
    _getMockData: () => mockData,
    _getMockExpirations: () => mockExpirations,
    _clearMocks: () => {
      mockData.clear();
      mockExpirations.clear();
    }
  };
}

/**
 * Redis test scenarios
 */
const redisScenarios = {
  // Test caching scenarios
  cacheHit: async (key, value) => {
    await setTestData(key, value);
    return await getTestData(key);
  },
  
  cacheMiss: async (key) => {
    return await getTestData(key);
  },
  
  cacheExpiration: async (key, value, seconds = 1) => {
    await setTestData(key, value, seconds);
    // Wait for expiration
    await new Promise(resolve => setTimeout(resolve, (seconds + 1) * 1000));
    return await getTestData(key);
  },
  
  // Test session scenarios
  sessionStorage: async (sessionId, sessionData) => {
    const key = `session:${sessionId}`;
    await setTestData(key, sessionData, 3600); // 1 hour
    return await getTestData(key);
  },
  
  // Test rate limiting scenarios
  rateLimitCounter: async (userId, limit = 10) => {
    const key = `rate_limit:${userId}`;
    const current = await getTestData(key) || 0;
    const newCount = current + 1;
    
    if (newCount <= limit) {
      await setTestData(key, newCount, 60); // 1 minute window
      return { allowed: true, count: newCount };
    } else {
      return { allowed: false, count: current };
    }
  }
};

/**
 * Get test Redis client
 */
function getTestRedisClient() {
  return testRedisClient;
}

module.exports = {
  setupTestRedis,
  cleanupTestRedis,
  clearTestRedisData,
  setTestData,
  getTestData,
  deleteTestData,
  keyExists,
  setExpiration,
  getTTL,
  createMockRedisClient,
  redisScenarios,
  getTestRedisClient
};
