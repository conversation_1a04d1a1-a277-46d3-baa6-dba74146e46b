{"name": "manufacturex-backend", "version": "1.0.0", "description": "ManufactureX Manufacturing Simulation Engine - Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --coverage", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=tests/integration", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write \"src/**/*.js\"", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all", "db:reset": "npx sequelize-cli db:migrate:undo:all && npm run db:migrate && npm run db:seed", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o swagger.json", "build": "echo 'No build step required for Node.js'", "docker:build": "docker build -t manufacturex-backend .", "docker:run": "docker run -p 5000:5000 manufacturex-backend"}, "keywords": ["manufacturing", "simulation", "digital-twin", "industry-4.0", "iot", "analytics", "optimization"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/ManufactureX.git"}, "bugs": {"url": "https://github.com/HectorTa1989/ManufactureX/issues"}, "homepage": "https://github.com/HectorTa1989/ManufactureX#readme", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "sequelize": "^6.35.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.10", "ioredis": "^5.3.2", "axios": "^1.6.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "xlsx": "^0.18.5", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "joi": "^17.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "nodemailer": "^6.9.7", "cron": "^3.1.6", "bull": "^4.12.2", "prometheus-client": "^15.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "sequelize-cli": "^6.6.2", "@types/jest": "^29.5.8", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "!src/config/**", "!src/migrations/**", "!src/seeders/**"], "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}