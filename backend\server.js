#!/usr/bin/env node

/**
 * ManufactureX Backend Server
 * Main entry point for the manufacturing simulation backend API
 */

const app = require('./src/app');
const config = require('./src/config/config');
const logger = require('./src/utils/logger');

const PORT = config.port || 5000;
const HOST = config.host || '0.0.0.0';

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, HOST, () => {
  logger.info(`ManufactureX Backend Server running on ${HOST}:${PORT}`);
  logger.info(`Environment: ${config.env}`);
  logger.info(`API Documentation: http://${HOST}:${PORT}/api-docs`);
});

// Handle server errors
server.on('error', (error) => {
  if (error.syscall !== 'listen') {
    throw error;
  }

  const bind = typeof PORT === 'string' ? 'Pipe ' + PORT : 'Port ' + PORT;

  switch (error.code) {
    case 'EACCES':
      logger.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;
    case 'EADDRINUSE':
      logger.error(`${bind} is already in use`);
      process.exit(1);
      break;
    default:
      throw error;
  }
});

module.exports = server;
