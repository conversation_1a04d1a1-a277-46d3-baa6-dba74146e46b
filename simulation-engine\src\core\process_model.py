"""
Process Model
Manufacturing process modeling and simulation logic
"""

import asyncio
import random
import math
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ProcessType(Enum):
    """Manufacturing process types"""
    ASSEMBLY = "assembly"
    MACHINING = "machining"
    PACKAGING = "packaging"
    QUALITY_CONTROL = "quality_control"
    MATERIAL_HANDLING = "material_handling"
    WELDING = "welding"
    PAINTING = "painting"
    INJECTION_MOLDING = "injection_molding"

class MachineState(Enum):
    """Machine state enumeration"""
    IDLE = "idle"
    RUNNING = "running"
    MAINTENANCE = "maintenance"
    BREAKDOWN = "breakdown"
    SETUP = "setup"

@dataclass
class ProcessState:
    """Current state of the manufacturing process"""
    timestamp: float
    units_produced: int = 0
    units_in_queue: int = 0
    active_machines: int = 0
    total_machines: int = 0
    defective_units: int = 0
    cycle_time: float = 0.0
    completed: bool = False
    metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}

@dataclass
class Machine:
    """Manufacturing machine representation"""
    machine_id: str
    machine_type: str
    state: MachineState = MachineState.IDLE
    efficiency: float = 1.0
    cycle_time: float = 60.0  # seconds
    failure_rate: float = 0.001  # failures per hour
    maintenance_hours: float = 0.0
    total_runtime: float = 0.0
    units_produced: int = 0
    
class ProcessModel:
    """Manufacturing process model"""
    
    def __init__(self, process_type: str, parameters: Dict[str, Any]):
        self.process_type = ProcessType(process_type.lower())
        self.parameters = parameters
        
        # Process state
        self.current_state = ProcessState(timestamp=0.0)
        self.machines: List[Machine] = []
        self.production_queue: List[Dict[str, Any]] = []
        
        # Process parameters
        self.target_throughput = parameters.get('target_throughput', 100)  # units/hour
        self.quality_target = parameters.get('quality_target', 95.0)  # percentage
        self.capacity = parameters.get('capacity', 1000)  # max units
        self.batch_size = parameters.get('batch_size', 10)
        
        # Initialize based on process type
        self._initialize_process_specific_parameters()
    
    def _initialize_process_specific_parameters(self):
        """Initialize parameters specific to process type"""
        if self.process_type == ProcessType.ASSEMBLY:
            self._initialize_assembly_process()
        elif self.process_type == ProcessType.MACHINING:
            self._initialize_machining_process()
        elif self.process_type == ProcessType.PACKAGING:
            self._initialize_packaging_process()
        elif self.process_type == ProcessType.QUALITY_CONTROL:
            self._initialize_qc_process()
        elif self.process_type == ProcessType.MATERIAL_HANDLING:
            self._initialize_material_handling_process()
        elif self.process_type == ProcessType.WELDING:
            self._initialize_welding_process()
        elif self.process_type == ProcessType.PAINTING:
            self._initialize_painting_process()
        elif self.process_type == ProcessType.INJECTION_MOLDING:
            self._initialize_injection_molding_process()
        else:
            self._initialize_generic_process()
    
    def _initialize_assembly_process(self):
        """Initialize assembly line process"""
        num_stations = self.parameters.get('num_stations', 5)
        cycle_time = self.parameters.get('cycle_time', 45.0)
        
        for i in range(num_stations):
            machine = Machine(
                machine_id=f"assembly_station_{i+1}",
                machine_type="assembly_station",
                cycle_time=cycle_time + random.uniform(-5, 5),
                efficiency=random.uniform(0.85, 0.95),
                failure_rate=0.0005
            )
            self.machines.append(machine)
    
    def _initialize_machining_process(self):
        """Initialize machining process"""
        num_machines = self.parameters.get('num_machines', 3)
        cycle_time = self.parameters.get('cycle_time', 120.0)
        
        machine_types = ['cnc_mill', 'cnc_lathe', 'drill_press']
        
        for i in range(num_machines):
            machine_type = machine_types[i % len(machine_types)]
            machine = Machine(
                machine_id=f"{machine_type}_{i+1}",
                machine_type=machine_type,
                cycle_time=cycle_time + random.uniform(-20, 20),
                efficiency=random.uniform(0.80, 0.90),
                failure_rate=0.002
            )
            self.machines.append(machine)
    
    def _initialize_packaging_process(self):
        """Initialize packaging process"""
        num_lines = self.parameters.get('num_lines', 2)
        cycle_time = self.parameters.get('cycle_time', 30.0)
        
        for i in range(num_lines):
            machine = Machine(
                machine_id=f"packaging_line_{i+1}",
                machine_type="packaging_line",
                cycle_time=cycle_time + random.uniform(-3, 3),
                efficiency=random.uniform(0.90, 0.98),
                failure_rate=0.0003
            )
            self.machines.append(machine)
    
    def _initialize_qc_process(self):
        """Initialize quality control process"""
        num_stations = self.parameters.get('num_stations', 2)
        inspection_time = self.parameters.get('inspection_time', 60.0)
        
        for i in range(num_stations):
            machine = Machine(
                machine_id=f"qc_station_{i+1}",
                machine_type="inspection_station",
                cycle_time=inspection_time + random.uniform(-10, 10),
                efficiency=random.uniform(0.95, 0.99),
                failure_rate=0.0001
            )
            self.machines.append(machine)
    
    def _initialize_material_handling_process(self):
        """Initialize material handling process"""
        num_robots = self.parameters.get('num_robots', 3)
        cycle_time = self.parameters.get('cycle_time', 20.0)
        
        for i in range(num_robots):
            machine = Machine(
                machine_id=f"robot_{i+1}",
                machine_type="material_handling_robot",
                cycle_time=cycle_time + random.uniform(-2, 2),
                efficiency=random.uniform(0.92, 0.98),
                failure_rate=0.0008
            )
            self.machines.append(machine)
    
    def _initialize_welding_process(self):
        """Initialize welding process"""
        num_stations = self.parameters.get('num_stations', 4)
        cycle_time = self.parameters.get('cycle_time', 90.0)
        
        for i in range(num_stations):
            machine = Machine(
                machine_id=f"welding_station_{i+1}",
                machine_type="welding_station",
                cycle_time=cycle_time + random.uniform(-15, 15),
                efficiency=random.uniform(0.85, 0.92),
                failure_rate=0.001
            )
            self.machines.append(machine)
    
    def _initialize_painting_process(self):
        """Initialize painting process"""
        num_booths = self.parameters.get('num_booths', 2)
        cycle_time = self.parameters.get('cycle_time', 180.0)
        
        for i in range(num_booths):
            machine = Machine(
                machine_id=f"paint_booth_{i+1}",
                machine_type="paint_booth",
                cycle_time=cycle_time + random.uniform(-30, 30),
                efficiency=random.uniform(0.88, 0.95),
                failure_rate=0.0015
            )
            self.machines.append(machine)
    
    def _initialize_injection_molding_process(self):
        """Initialize injection molding process"""
        num_machines = self.parameters.get('num_machines', 6)
        cycle_time = self.parameters.get('cycle_time', 75.0)
        
        for i in range(num_machines):
            machine = Machine(
                machine_id=f"injection_machine_{i+1}",
                machine_type="injection_molding_machine",
                cycle_time=cycle_time + random.uniform(-10, 10),
                efficiency=random.uniform(0.87, 0.94),
                failure_rate=0.0012
            )
            self.machines.append(machine)
    
    def _initialize_generic_process(self):
        """Initialize generic manufacturing process"""
        num_machines = self.parameters.get('num_machines', 3)
        cycle_time = self.parameters.get('cycle_time', 60.0)
        
        for i in range(num_machines):
            machine = Machine(
                machine_id=f"machine_{i+1}",
                machine_type="generic_machine",
                cycle_time=cycle_time + random.uniform(-10, 10),
                efficiency=random.uniform(0.85, 0.95),
                failure_rate=0.001
            )
            self.machines.append(machine)
    
    async def initialize(self):
        """Initialize the process model"""
        try:
            logger.info(f"Initializing {self.process_type.value} process model")
            
            # Initialize machine states
            for machine in self.machines:
                machine.state = MachineState.IDLE
                machine.total_runtime = 0.0
                machine.units_produced = 0
                machine.maintenance_hours = 0.0
            
            # Initialize production queue
            self.production_queue.clear()
            
            # Reset current state
            self.current_state = ProcessState(
                timestamp=0.0,
                total_machines=len(self.machines)
            )
            
            logger.info(f"Process model initialized with {len(self.machines)} machines")
            
        except Exception as e:
            logger.error(f"Failed to initialize process model: {e}")
            raise
    
    async def execute_step(self, current_time: float, step_size: float) -> ProcessState:
        """Execute a single process step"""
        try:
            # Update machine states
            await self._update_machine_states(current_time, step_size)
            
            # Process production queue
            await self._process_production_queue(current_time, step_size)
            
            # Generate new work orders
            await self._generate_work_orders(current_time, step_size)
            
            # Calculate process metrics
            metrics = await self._calculate_process_metrics(current_time, step_size)
            
            # Update current state
            self.current_state = ProcessState(
                timestamp=current_time,
                units_produced=sum(m.units_produced for m in self.machines),
                units_in_queue=len(self.production_queue),
                active_machines=sum(1 for m in self.machines if m.state == MachineState.RUNNING),
                total_machines=len(self.machines),
                defective_units=metrics.get('defective_units', 0),
                cycle_time=metrics.get('average_cycle_time', 0),
                completed=self._check_completion_criteria(),
                metrics=metrics
            )
            
            return self.current_state
            
        except Exception as e:
            logger.error(f"Process step execution failed: {e}")
            raise
    
    async def _update_machine_states(self, current_time: float, step_size: float):
        """Update the state of all machines"""
        for machine in self.machines:
            # Update runtime
            if machine.state == MachineState.RUNNING:
                machine.total_runtime += step_size
            
            # Check for breakdowns
            if machine.state == MachineState.RUNNING:
                breakdown_probability = machine.failure_rate * (step_size / 3600)
                if random.random() < breakdown_probability:
                    machine.state = MachineState.BREAKDOWN
                    logger.info(f"Machine {machine.machine_id} broke down")
                    continue
            
            # Handle breakdown recovery
            if machine.state == MachineState.BREAKDOWN:
                # Random repair time (simplified)
                if random.random() < 0.1:  # 10% chance to repair each step
                    machine.state = MachineState.IDLE
                    logger.info(f"Machine {machine.machine_id} repaired")
                continue
            
            # Handle maintenance
            if machine.state == MachineState.MAINTENANCE:
                machine.maintenance_hours += step_size / 3600
                if machine.maintenance_hours >= 2.0:  # 2 hours maintenance
                    machine.state = MachineState.IDLE
                    machine.maintenance_hours = 0.0
                    logger.info(f"Machine {machine.machine_id} maintenance completed")
                continue
            
            # Assign work if idle and queue not empty
            if machine.state == MachineState.IDLE and self.production_queue:
                machine.state = MachineState.RUNNING
                # Assign work order to machine
                work_order = self.production_queue.pop(0)
                work_order['assigned_machine'] = machine.machine_id
                work_order['start_time'] = current_time
    
    async def _process_production_queue(self, current_time: float, step_size: float):
        """Process items in the production queue"""
        completed_orders = []
        
        for work_order in self.production_queue:
            if 'assigned_machine' not in work_order:
                continue
            
            machine_id = work_order['assigned_machine']
            machine = next((m for m in self.machines if m.machine_id == machine_id), None)
            
            if not machine or machine.state != MachineState.RUNNING:
                continue
            
            # Check if work order is completed
            elapsed_time = current_time - work_order.get('start_time', current_time)
            required_time = machine.cycle_time * work_order.get('quantity', 1)
            
            if elapsed_time >= required_time:
                # Complete the work order
                machine.units_produced += work_order.get('quantity', 1)
                machine.state = MachineState.IDLE
                completed_orders.append(work_order)
        
        # Remove completed orders
        for order in completed_orders:
            if order in self.production_queue:
                self.production_queue.remove(order)
    
    async def _generate_work_orders(self, current_time: float, step_size: float):
        """Generate new work orders based on demand"""
        # Simple demand generation
        demand_rate = self.target_throughput / 3600  # units per second
        new_orders = int(demand_rate * step_size)
        
        # Add some randomness
        if random.random() < 0.3:
            new_orders += random.randint(0, 2)
        
        for _ in range(new_orders):
            work_order = {
                'order_id': f"WO_{current_time}_{random.randint(1000, 9999)}",
                'quantity': random.randint(1, self.batch_size),
                'priority': random.choice(['low', 'normal', 'high']),
                'created_time': current_time
            }
            self.production_queue.append(work_order)
    
    async def _calculate_process_metrics(self, current_time: float, step_size: float) -> Dict[str, Any]:
        """Calculate process-specific metrics"""
        total_units = sum(m.units_produced for m in self.machines)
        
        # Calculate defective units based on quality target
        quality_variance = random.uniform(-0.02, 0.02)  # ±2% variance
        actual_quality = self.quality_target + quality_variance
        defective_units = int(total_units * (1 - actual_quality / 100))
        
        # Calculate average cycle time
        if self.machines:
            avg_cycle_time = sum(m.cycle_time for m in self.machines) / len(self.machines)
        else:
            avg_cycle_time = 0.0
        
        # Calculate theoretical output
        theoretical_output = (current_time / avg_cycle_time) * len(self.machines) if avg_cycle_time > 0 else 0
        
        return {
            'actual_output': total_units,
            'theoretical_output': theoretical_output,
            'defective_units': defective_units,
            'total_units': total_units,
            'average_cycle_time': avg_cycle_time,
            'queue_length': len(self.production_queue),
            'active_machines': sum(1 for m in self.machines if m.state == MachineState.RUNNING),
            'total_machines': len(self.machines),
            'units_produced': total_units,
            'material_cost_per_unit': self.parameters.get('material_cost', 10.0),
            'labor_cost_per_hour': self.parameters.get('labor_cost', 25.0),
            'machine_cost_per_hour': self.parameters.get('machine_cost', 50.0),
            'active_workers': self.parameters.get('workers', len(self.machines)),
            'total_workers': self.parameters.get('workers', len(self.machines))
        }
    
    def _check_completion_criteria(self) -> bool:
        """Check if the process should be completed"""
        total_units = sum(m.units_produced for m in self.machines)
        
        # Complete if capacity is reached
        if total_units >= self.capacity:
            return True
        
        # Complete if all machines are broken down
        if all(m.state == MachineState.BREAKDOWN for m in self.machines):
            return True
        
        return False
