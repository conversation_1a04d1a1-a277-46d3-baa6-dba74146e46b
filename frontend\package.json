{"name": "manufacturex-frontend", "version": "1.0.0", "description": "ManufactureX Manufacturing Simulation Engine - Frontend Dashboard", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "docker:build": "docker build -t manufacturex-frontend .", "docker:run": "docker run -p 3000:3000 manufacturex-frontend"}, "keywords": ["manufacturing", "simulation", "digital-twin", "dashboard", "react", "vite"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/ManufactureX.git"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "recharts": "^2.8.0", "plotly.js": "^2.27.1", "react-plotly.js": "^2.6.0", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "framer-motion": "^10.16.5", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "react-datepicker": "^4.24.0", "react-table": "^7.8.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "js-cookie": "^3.0.5", "react-helmet-async": "^1.3.0", "react-error-boundary": "^4.0.11", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash": "^4.14.202", "@types/js-cookie": "^3.0.6", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "c8": "^8.0.1", "jsdom": "^23.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "typescript": "^5.2.2", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "husky": "^8.0.3", "lint-staged": "^15.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm test"}}}