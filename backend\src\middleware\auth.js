/**
 * Authentication Middleware
 * JWT-based authentication and authorization
 */

const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const config = require('../config/config');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errors');
const { asyncHandler } = require('./errorHandler');

/**
 * Protect routes - require authentication
 */
const protect = asyncHandler(async (req, res, next) => {
  // 1) Getting token and check if it's there
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies?.jwt) {
    token = req.cookies.jwt;
  }

  if (!token) {
    logger.logSecurity('Authentication attempt without token', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl
    });
    return next(new AppError('You are not logged in! Please log in to get access.', 401));
  }

  try {
    // 2) Verification token
    const decoded = await promisify(jwt.verify)(token, config.jwt.secret);

    // 3) Check if user still exists (in a real app, you'd check the database)
    // For now, we'll assume the user exists if the token is valid
    const currentUser = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role || 'user',
      iat: decoded.iat
    };

    // 4) Check if user changed password after the token was issued
    // This would typically involve checking a passwordChangedAt field in the database
    
    // 5) Grant access to protected route
    req.user = currentUser;
    res.locals.user = currentUser;
    
    logger.logSecurity('Successful authentication', {
      userId: currentUser.id,
      ip: req.ip,
      path: req.originalUrl
    });
    
    next();
  } catch (error) {
    logger.logSecurity('Failed authentication attempt', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl
    });
    
    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid token. Please log in again!', 401));
    } else if (error.name === 'TokenExpiredError') {
      return next(new AppError('Your token has expired! Please log in again.', 401));
    }
    
    return next(new AppError('Authentication failed', 401));
  }
});

/**
 * Restrict to certain roles
 */
const restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      logger.logSecurity('Authorization failure', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        resource: req.originalUrl,
        action: req.method
      });
      
      return next(
        new AppError('You do not have permission to perform this action', 403)
      );
    }
    next();
  };
};

/**
 * Optional authentication - doesn't fail if no token
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies?.jwt) {
    token = req.cookies.jwt;
  }

  if (token) {
    try {
      const decoded = await promisify(jwt.verify)(token, config.jwt.secret);
      req.user = {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role || 'user',
        iat: decoded.iat
      };
      res.locals.user = req.user;
    } catch (error) {
      // Token is invalid, but we don't fail - just continue without user
      logger.logSecurity('Invalid optional token', {
        error: error.message,
        ip: req.ip
      });
    }
  }
  
  next();
});

/**
 * Check if user owns resource
 */
const checkOwnership = (resourceUserIdField = 'userId') => {
  return asyncHandler(async (req, res, next) => {
    // Admin can access everything
    if (req.user.role === 'admin') {
      return next();
    }

    // For other users, check ownership
    const resourceUserId = req.body[resourceUserIdField] || 
                          req.params[resourceUserIdField] || 
                          req.query[resourceUserIdField];

    if (resourceUserId && resourceUserId !== req.user.id) {
      logger.logSecurity('Ownership check failed', {
        userId: req.user.id,
        resourceUserId,
        resource: req.originalUrl
      });
      
      return next(new AppError('You can only access your own resources', 403));
    }

    next();
  });
};

/**
 * Rate limiting per user
 */
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    if (userRequests.has(userId)) {
      const requests = userRequests.get(userId).filter(time => time > windowStart);
      userRequests.set(userId, requests);
    } else {
      userRequests.set(userId, []);
    }

    const userRequestCount = userRequests.get(userId).length;

    if (userRequestCount >= maxRequests) {
      logger.logSecurity('User rate limit exceeded', {
        userId,
        requestCount: userRequestCount,
        maxRequests,
        ip: req.ip
      });
      
      return next(new AppError('Too many requests. Please try again later.', 429));
    }

    // Add current request
    userRequests.get(userId).push(now);
    next();
  };
};

/**
 * API Key authentication (for external services)
 */
const apiKeyAuth = asyncHandler(async (req, res, next) => {
  const apiKey = req.headers['x-api-key'] || req.query.apiKey;

  if (!apiKey) {
    return next(new AppError('API key is required', 401));
  }

  // In a real application, you would validate the API key against a database
  const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];
  
  if (!validApiKeys.includes(apiKey)) {
    logger.logSecurity('Invalid API key attempt', {
      apiKey: apiKey.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return next(new AppError('Invalid API key', 401));
  }

  // Set a service user for API key requests
  req.user = {
    id: 'api-service',
    email: '<EMAIL>',
    role: 'service',
    type: 'api-key'
  };

  next();
});

/**
 * Generate JWT token
 */
const signToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

/**
 * Generate refresh token
 */
const signRefreshToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });
};

/**
 * Create and send token response
 */
const createSendToken = (user, statusCode, res) => {
  const token = signToken({ 
    id: user.id, 
    email: user.email, 
    role: user.role 
  });
  
  const refreshToken = signRefreshToken({ 
    id: user.id, 
    email: user.email, 
    role: user.role 
  });

  const cookieOptions = {
    expires: new Date(
      Date.now() + config.security.cookieMaxAge
    ),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  };

  res.cookie('jwt', token, cookieOptions);

  // Remove password from output
  user.password = undefined;

  res.status(statusCode).json({
    success: true,
    token,
    refreshToken,
    data: {
      user
    }
  });
};

module.exports = {
  protect,
  restrictTo,
  optionalAuth,
  checkOwnership,
  userRateLimit,
  apiKeyAuth,
  signToken,
  signRefreshToken,
  createSendToken
};
