# ManufactureX MQTT Broker Configuration
# Eclipse Mosquitto configuration for IoT device communication

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Process ID file location
pid_file /var/run/mosquitto.pid

# Persistence settings
persistence true
persistence_location /mosquitto/data/

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout
log_type error
log_type warning
log_type notice
log_type information
log_timestamp true
log_timestamp_format %Y-%m-%dT%H:%M:%S

# Connection logging
connection_messages true

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Default MQTT port
listener 1883 0.0.0.0
protocol mqtt

# WebSocket support
listener 9001 0.0.0.0
protocol websockets

# Maximum number of client connections
max_connections 1000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Allow anonymous connections (for development)
# In production, set this to false and configure authentication
allow_anonymous true

# Password file (uncomment for production)
# password_file /mosquitto/config/passwd

# Access Control List (uncomment for production)
# acl_file /mosquitto/config/acl

# =============================================================================
# MESSAGE CONFIGURATION
# =============================================================================

# Maximum message size (1MB)
message_size_limit 1048576

# Maximum number of QoS 1 and 2 messages to hold in memory per client
max_inflight_messages 20

# Maximum number of QoS 1 and 2 messages to hold in the queue
max_queued_messages 1000

# =============================================================================
# PERSISTENCE CONFIGURATION
# =============================================================================

# Save in-memory database to disk every 1800 seconds
autosave_interval 1800

# Save in-memory database to disk if number of subscription changes exceeds this value
autosave_on_changes false

# =============================================================================
# BRIDGE CONFIGURATION (for external MQTT brokers)
# =============================================================================

# Example bridge configuration (uncomment if needed)
# connection bridge-01
# address external-broker.example.com:1883
# topic sensors/# out 0
# topic commands/# in 0

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================

# WebSocket protocol name
websockets_log_level 255

# =============================================================================
# CLIENT CONFIGURATION
# =============================================================================

# Client connection timeout
keepalive_interval 60

# Maximum time to wait for CONNECT message
connect_timeout 10

# =============================================================================
# TOPIC CONFIGURATION
# =============================================================================

# Maximum topic length
max_topic_length 65535

# =============================================================================
# WILL MESSAGE CONFIGURATION
# =============================================================================

# Maximum will message size
max_will_message_size 1048576

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable detailed logging for development
# log_type debug
# log_type subscribe
# log_type unsubscribe

# =============================================================================
# PRODUCTION SECURITY SETTINGS (uncomment for production)
# =============================================================================

# Disable anonymous access
# allow_anonymous false

# Require client certificates
# require_certificate true
# cafile /mosquitto/config/ca.crt
# certfile /mosquitto/config/server.crt
# keyfile /mosquitto/config/server.key

# TLS settings
# tls_version tlsv1.2

# =============================================================================
# PLUGIN CONFIGURATION
# =============================================================================

# Authentication plugin (example)
# auth_plugin /usr/lib/mosquitto_auth_plugin.so
# auth_opt_backends mysql
# auth_opt_host localhost
# auth_opt_port 3306
# auth_opt_dbname mosquitto
# auth_opt_user mosquitto
# auth_opt_pass mosquitto

# =============================================================================
# CUSTOM TOPIC PATTERNS FOR MANUFACTUREX
# =============================================================================

# Manufacturing sensor data: sensors/{machine_id}/{sensor_type}
# Example: sensors/cnc_001/temperature

# Machine commands: commands/{machine_id}/{command_type}
# Example: commands/cnc_001/start

# Alerts and alarms: alerts/{severity}/{machine_id}
# Example: alerts/critical/cnc_001

# Production data: production/{line_id}/{metric}
# Example: production/line_a/count

# Quality data: quality/{station_id}/{result}
# Example: quality/inspection_01/pass
