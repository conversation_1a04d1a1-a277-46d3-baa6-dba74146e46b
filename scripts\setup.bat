@echo off
REM ManufactureX Setup Script for Windows
REM Automated setup for development environment

echo.
echo ========================================
echo ManufactureX Setup Script
echo ========================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop and try again
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    docker-compose --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: Docker Compose is not available
        echo Please install Docker Compose and try again
        pause
        exit /b 1
    )
    set COMPOSE_CMD=docker-compose
) else (
    set COMPOSE_CMD=docker compose
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js and try again
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11+ and try again
    pause
    exit /b 1
)

echo All required dependencies are installed
echo.

REM Create .env file if it doesn't exist
if not exist .env (
    if exist .env.example (
        echo Creating .env file from .env.example...
        copy .env.example .env
        echo WARNING: Please review and update the environment variables in .env
    ) else (
        echo ERROR: .env.example not found
        pause
        exit /b 1
    )
) else (
    echo .env file already exists, skipping creation
)

REM Create necessary directories
echo Creating necessary directories...
if not exist logs mkdir logs
if not exist uploads mkdir uploads
if not exist data mkdir data
if not exist temp mkdir temp
if not exist models mkdir models
if not exist backups mkdir backups
if not exist docker\mosquitto\config mkdir docker\mosquitto\config
if not exist docker\mosquitto\data mkdir docker\mosquitto\data
if not exist docker\mosquitto\log mkdir docker\mosquitto\log
if not exist docker\nginx\conf.d mkdir docker\nginx\conf.d
if not exist docker\prometheus mkdir docker\prometheus
if not exist docker\grafana\provisioning mkdir docker\grafana\provisioning
if not exist docker\grafana\dashboards mkdir docker\grafana\dashboards

echo Directories created successfully
echo.

REM Install backend dependencies
echo Installing backend dependencies...
cd backend
if exist package.json (
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install backend dependencies
        cd ..
        pause
        exit /b 1
    )
    echo Backend dependencies installed successfully
) else (
    echo ERROR: package.json not found in backend directory
    cd ..
    pause
    exit /b 1
)
cd ..

REM Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
if exist package.json (
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        cd ..
        pause
        exit /b 1
    )
    echo Frontend dependencies installed successfully
) else (
    echo ERROR: package.json not found in frontend directory
    cd ..
    pause
    exit /b 1
)
cd ..

REM Install Python dependencies
echo Installing Python dependencies...
cd simulation-engine
if exist requirements.txt (
    REM Create virtual environment if it doesn't exist
    if not exist venv (
        echo Creating Python virtual environment...
        python -m venv venv
    )
    
    REM Activate virtual environment and install dependencies
    echo Activating virtual environment and installing dependencies...
    call venv\Scripts\activate.bat
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Python dependencies
        deactivate
        cd ..
        pause
        exit /b 1
    )
    deactivate
    echo Python dependencies installed successfully
) else (
    echo ERROR: requirements.txt not found in simulation-engine directory
    cd ..
    pause
    exit /b 1
)
cd ..

REM Build and start Docker services
echo Building and starting Docker services...
%COMPOSE_CMD% build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Docker images
    pause
    exit /b 1
)

echo Starting core services...
%COMPOSE_CMD% up -d postgres redis mqtt influxdb
if %errorlevel% neq 0 (
    echo ERROR: Failed to start core services
    pause
    exit /b 1
)

echo Waiting for services to be ready...
timeout /t 15 /nobreak >nul

echo Starting application services...
%COMPOSE_CMD% up -d simulation-engine backend frontend
if %errorlevel% neq 0 (
    echo ERROR: Failed to start application services
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Service URLs:
echo   Frontend:          http://localhost:3000
echo   Backend API:       http://localhost:5000
echo   Simulation Engine: http://localhost:8000
echo   API Documentation: http://localhost:5000/api-docs
echo.
echo Management URLs:
echo   InfluxDB:          http://localhost:8086
echo   Grafana:           http://localhost:3001 (admin/admin123)
echo   Portainer:         http://localhost:9000
echo.
echo Useful commands:
echo   View logs:         %COMPOSE_CMD% logs -f [service]
echo   Stop services:     %COMPOSE_CMD% down
echo   Restart services:  %COMPOSE_CMD% restart
echo   Update services:   %COMPOSE_CMD% pull && %COMPOSE_CMD% up -d
echo.
echo IMPORTANT NOTES:
echo   - Review and update environment variables in .env
echo   - Change default passwords in production
echo   - Services may take a few minutes to fully start
echo.
echo Press any key to exit...
pause >nul
