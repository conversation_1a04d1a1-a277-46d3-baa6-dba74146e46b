"""
Rate Limiting Middleware for Simulation Engine
Implements various rate limiting strategies for FastAPI
"""

import time
import asyncio
from typing import Dict, Optional, Callable, Any
from collections import defaultdict, deque
from dataclasses import dataclass, field
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import redis.asyncio as redis
from src.utils.logger import logger
from src.utils.config import Config

config = Config()

@dataclass
class RateLimitInfo:
    """Rate limit information"""
    requests: deque = field(default_factory=deque)
    last_request: float = 0.0
    blocked_until: float = 0.0
    violation_count: int = 0

class RateLimiter:
    """Base rate limiter class"""
    
    def __init__(
        self,
        max_requests: int,
        window_seconds: int,
        block_duration: int = 60,
        redis_client: Optional[redis.Redis] = None
    ):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.block_duration = block_duration
        self.redis_client = redis_client
        self.local_storage: Dict[str, RateLimitInfo] = defaultdict(RateLimitInfo)
        
    async def is_allowed(self, key: str) -> tuple[bool, Dict[str, Any]]:
        """Check if request is allowed"""
        if self.redis_client:
            return await self._check_redis(key)
        else:
            return await self._check_local(key)
    
    async def _check_local(self, key: str) -> tuple[bool, Dict[str, Any]]:
        """Check rate limit using local storage"""
        now = time.time()
        info = self.local_storage[key]
        
        # Check if currently blocked
        if now < info.blocked_until:
            remaining_block = int(info.blocked_until - now)
            return False, {
                'allowed': False,
                'limit': self.max_requests,
                'remaining': 0,
                'reset_time': int(info.blocked_until),
                'retry_after': remaining_block,
                'blocked': True
            }
        
        # Clean old requests
        cutoff = now - self.window_seconds
        while info.requests and info.requests[0] <= cutoff:
            info.requests.popleft()
        
        # Check if limit exceeded
        if len(info.requests) >= self.max_requests:
            info.violation_count += 1
            info.blocked_until = now + self.block_duration
            
            logger.warning(f"Rate limit exceeded for key: {key}", extra={
                'key': key,
                'requests': len(info.requests),
                'limit': self.max_requests,
                'window': self.window_seconds,
                'violations': info.violation_count
            })
            
            return False, {
                'allowed': False,
                'limit': self.max_requests,
                'remaining': 0,
                'reset_time': int(now + self.window_seconds),
                'retry_after': self.block_duration,
                'blocked': True
            }
        
        # Allow request
        info.requests.append(now)
        info.last_request = now
        remaining = self.max_requests - len(info.requests)
        
        return True, {
            'allowed': True,
            'limit': self.max_requests,
            'remaining': remaining,
            'reset_time': int(now + self.window_seconds),
            'retry_after': 0,
            'blocked': False
        }
    
    async def _check_redis(self, key: str) -> tuple[bool, Dict[str, Any]]:
        """Check rate limit using Redis"""
        try:
            pipe = self.redis_client.pipeline()
            now = time.time()
            window_start = now - self.window_seconds
            
            # Use Redis sorted set for sliding window
            redis_key = f"rate_limit:{key}"
            
            # Remove old entries
            pipe.zremrangebyscore(redis_key, 0, window_start)
            
            # Count current requests
            pipe.zcard(redis_key)
            
            # Add current request
            pipe.zadd(redis_key, {str(now): now})
            
            # Set expiration
            pipe.expire(redis_key, self.window_seconds + 1)
            
            results = await pipe.execute()
            current_requests = results[1]
            
            if current_requests >= self.max_requests:
                # Remove the request we just added since it's not allowed
                await self.redis_client.zrem(redis_key, str(now))
                
                logger.warning(f"Rate limit exceeded for key: {key}", extra={
                    'key': key,
                    'requests': current_requests,
                    'limit': self.max_requests,
                    'window': self.window_seconds
                })
                
                return False, {
                    'allowed': False,
                    'limit': self.max_requests,
                    'remaining': 0,
                    'reset_time': int(now + self.window_seconds),
                    'retry_after': self.window_seconds,
                    'blocked': False
                }
            
            remaining = self.max_requests - current_requests - 1
            
            return True, {
                'allowed': True,
                'limit': self.max_requests,
                'remaining': remaining,
                'reset_time': int(now + self.window_seconds),
                'retry_after': 0,
                'blocked': False
            }
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to local storage
            return await self._check_local(key)

class SlidingWindowRateLimiter(RateLimiter):
    """Sliding window rate limiter"""
    
    async def _check_local(self, key: str) -> tuple[bool, Dict[str, Any]]:
        """Sliding window implementation"""
        now = time.time()
        info = self.local_storage[key]
        
        # Clean old requests (sliding window)
        cutoff = now - self.window_seconds
        while info.requests and info.requests[0] <= cutoff:
            info.requests.popleft()
        
        if len(info.requests) >= self.max_requests:
            # Calculate when the oldest request will expire
            oldest_request = info.requests[0] if info.requests else now
            retry_after = int(oldest_request + self.window_seconds - now)
            
            return False, {
                'allowed': False,
                'limit': self.max_requests,
                'remaining': 0,
                'reset_time': int(oldest_request + self.window_seconds),
                'retry_after': max(1, retry_after),
                'blocked': False
            }
        
        info.requests.append(now)
        remaining = self.max_requests - len(info.requests)
        
        return True, {
            'allowed': True,
            'limit': self.max_requests,
            'remaining': remaining,
            'reset_time': int(now + self.window_seconds),
            'retry_after': 0,
            'blocked': False
        }

class TokenBucketRateLimiter:
    """Token bucket rate limiter for burst handling"""
    
    def __init__(
        self,
        capacity: int,
        refill_rate: float,
        redis_client: Optional[redis.Redis] = None
    ):
        self.capacity = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.redis_client = redis_client
        self.buckets: Dict[str, Dict[str, float]] = defaultdict(
            lambda: {'tokens': capacity, 'last_refill': time.time()}
        )
    
    async def is_allowed(self, key: str, tokens_requested: int = 1) -> tuple[bool, Dict[str, Any]]:
        """Check if request is allowed using token bucket"""
        if self.redis_client:
            return await self._check_redis(key, tokens_requested)
        else:
            return await self._check_local(key, tokens_requested)
    
    async def _check_local(self, key: str, tokens_requested: int) -> tuple[bool, Dict[str, Any]]:
        """Local token bucket implementation"""
        now = time.time()
        bucket = self.buckets[key]
        
        # Refill tokens
        time_passed = now - bucket['last_refill']
        tokens_to_add = time_passed * self.refill_rate
        bucket['tokens'] = min(self.capacity, bucket['tokens'] + tokens_to_add)
        bucket['last_refill'] = now
        
        if bucket['tokens'] >= tokens_requested:
            bucket['tokens'] -= tokens_requested
            return True, {
                'allowed': True,
                'tokens_remaining': int(bucket['tokens']),
                'capacity': self.capacity,
                'refill_rate': self.refill_rate
            }
        else:
            # Calculate retry after
            tokens_needed = tokens_requested - bucket['tokens']
            retry_after = int(tokens_needed / self.refill_rate)
            
            return False, {
                'allowed': False,
                'tokens_remaining': int(bucket['tokens']),
                'capacity': self.capacity,
                'refill_rate': self.refill_rate,
                'retry_after': retry_after
            }
    
    async def _check_redis(self, key: str, tokens_requested: int) -> tuple[bool, Dict[str, Any]]:
        """Redis token bucket implementation using Lua script"""
        lua_script = """
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local refill_rate = tonumber(ARGV[2])
        local tokens_requested = tonumber(ARGV[3])
        local now = tonumber(ARGV[4])
        
        local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
        local tokens = tonumber(bucket[1]) or capacity
        local last_refill = tonumber(bucket[2]) or now
        
        -- Refill tokens
        local time_passed = now - last_refill
        local tokens_to_add = time_passed * refill_rate
        tokens = math.min(capacity, tokens + tokens_to_add)
        
        if tokens >= tokens_requested then
            tokens = tokens - tokens_requested
            redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
            redis.call('EXPIRE', key, 3600)  -- 1 hour expiration
            return {1, tokens}
        else
            redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
            redis.call('EXPIRE', key, 3600)
            return {0, tokens}
        end
        """
        
        try:
            result = await self.redis_client.eval(
                lua_script,
                1,
                f"token_bucket:{key}",
                self.capacity,
                self.refill_rate,
                tokens_requested,
                time.time()
            )
            
            allowed = bool(result[0])
            tokens_remaining = int(result[1])
            
            if allowed:
                return True, {
                    'allowed': True,
                    'tokens_remaining': tokens_remaining,
                    'capacity': self.capacity,
                    'refill_rate': self.refill_rate
                }
            else:
                tokens_needed = tokens_requested - tokens_remaining
                retry_after = int(tokens_needed / self.refill_rate)
                
                return False, {
                    'allowed': False,
                    'tokens_remaining': tokens_remaining,
                    'capacity': self.capacity,
                    'refill_rate': self.refill_rate,
                    'retry_after': retry_after
                }
                
        except Exception as e:
            logger.error(f"Redis token bucket error: {e}")
            return await self._check_local(key, tokens_requested)

# Rate limiter instances
general_limiter = SlidingWindowRateLimiter(
    max_requests=config.get('RATE_LIMIT_REQUESTS', 100),
    window_seconds=config.get('RATE_LIMIT_WINDOW', 60)
)

simulation_limiter = SlidingWindowRateLimiter(
    max_requests=20,
    window_seconds=300  # 5 minutes
)

heavy_operation_limiter = SlidingWindowRateLimiter(
    max_requests=5,
    window_seconds=3600  # 1 hour
)

burst_limiter = TokenBucketRateLimiter(
    capacity=50,
    refill_rate=1.0  # 1 token per second
)

def get_client_key(request: Request) -> str:
    """Generate client key for rate limiting"""
    # Try to get user ID from request
    user_id = getattr(request.state, 'user_id', None)
    if user_id:
        return f"user:{user_id}"
    
    # Fallback to IP address
    client_ip = request.client.host
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        client_ip = forwarded_for.split(',')[0].strip()
    
    return f"ip:{client_ip}"

async def rate_limit_middleware(
    request: Request,
    call_next: Callable,
    limiter: RateLimiter = general_limiter
) -> Any:
    """Rate limiting middleware"""
    
    # Skip rate limiting for health checks
    if request.url.path in ['/health', '/metrics']:
        return await call_next(request)
    
    client_key = get_client_key(request)
    allowed, info = await limiter.is_allowed(client_key)
    
    if not allowed:
        logger.warning(f"Rate limit exceeded for {client_key}", extra={
            'client_key': client_key,
            'path': request.url.path,
            'method': request.method,
            'rate_limit_info': info
        })
        
        headers = {
            'X-RateLimit-Limit': str(info.get('limit', 0)),
            'X-RateLimit-Remaining': str(info.get('remaining', 0)),
            'X-RateLimit-Reset': str(info.get('reset_time', 0)),
            'Retry-After': str(info.get('retry_after', 60))
        }
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                'error': 'Rate limit exceeded',
                'message': 'Too many requests, please try again later',
                'retry_after': info.get('retry_after', 60),
                'limit': info.get('limit', 0),
                'remaining': info.get('remaining', 0)
            },
            headers=headers
        )
    
    # Add rate limit headers to response
    response = await call_next(request)
    response.headers['X-RateLimit-Limit'] = str(info.get('limit', 0))
    response.headers['X-RateLimit-Remaining'] = str(info.get('remaining', 0))
    response.headers['X-RateLimit-Reset'] = str(info.get('reset_time', 0))
    
    return response

# Decorator for specific endpoints
def rate_limit(limiter: RateLimiter = general_limiter):
    """Rate limiting decorator"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            client_key = get_client_key(request)
            allowed, info = await limiter.is_allowed(client_key)
            
            if not allowed:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        'error': 'Rate limit exceeded',
                        'retry_after': info.get('retry_after', 60)
                    },
                    headers={
                        'Retry-After': str(info.get('retry_after', 60))
                    }
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
